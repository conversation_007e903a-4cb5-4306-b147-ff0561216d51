# Word试题解析服务使用说明

![Node.js](https://img.shields.io/badge/Node.js-%3E%3D12.0.0-green)
![TypeScript](https://img.shields.io/badge/TypeScript-4.8.0-blue)
![Midway.js](https://img.shields.io/badge/Midway.js-3.12.0-orange)
![License](https://img.shields.io/badge/License-MIT-yellow)
![Test](https://img.shields.io/badge/Tests-90%25%20Pass-brightgreen)

## 🚀 快速开始

### ⚡ 30秒体验（推荐）

```bash
# 1. 安装依赖
npm install

# 2. 最快验证方式 - Word导出测试
node --require ts-node/register src/example/word-export-example.ts

# 3. 检查结果
ls -la testFiles/output-word-result.docx
```

成功运行后会在 `testFiles/` 目录下生成 `output-word-result.docx` 文件。

### 📋 完整功能验证

```bash
# 1. 克隆项目
git clone <repository-url>
cd question-import-word

# 2. 安装依赖
npm install

# 3. 一键验证所有功能
node verify.js

# 4. 测试Word解析功能（生成JSON和HTML预览）
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx

# 5. 测试Word导出功能
node --require ts-node/register src/example/word-export-example.ts

# 6. 启动开发服务器
npm run dev
```

### 🎬 使用演示

```
📁 项目目录
├── 📄 testFiles/input-html-sample.html    ← 输入：HTML文件
├── 🔄 运行命令...                          ← 处理：解析转换
└── 📄 testFiles/output-word-result.docx   ← 输出：Word文档

💡 整个过程：
   HTML文件 → 解析样式 → 转换格式 → 生成Word文档

⏱️ 处理时间：通常 < 3秒
📊 支持内容：文字、表格、图片、列表、样式
```

### 🛠️ 一键验证脚本

我们提供了自动验证脚本，一键检测所有功能：

```bash
# 运行完整验证（推荐）
node verify.js
```

该脚本会自动：
- ✅ 检查环境依赖
- ✅ 安装项目依赖
- ✅ 编译TypeScript
- ✅ 运行功能测试
- ✅ 验证输出结果
- ✅ 生成测试报告

## 📋 目录

- [🚀 快速开始](#-快速开始)
- [功能概述](#功能概述)
- [使用方法](#使用方法)
- [快速验证](#快速验证)
- [📝 命令参考](#-命令参考)
- [输出格式](#输出格式)
- [注意事项](#注意事项)
- [技术实现](#技术实现)
- [项目结构](#项目结构)
- [支持的HTML特性](#支持的-html-特性)
- [开发](#开发)
- [后续优化方向](#后续优化方向)

---

## 功能概述

本服务基于Midway.js框架开发，提供了强大的Word文档(.docx)解析功能，能够保留文档中的颜色、格式、图片、表格和数学公式等信息，并将其转换为HTML格式或标准化的JSON格式。主要功能包括：

- 解析.docx文件中的段落和样式，保留原始格式和颜色
- 处理数学公式（支持LaTeX渲染）
- 提取和处理图片（保留位置和大小信息）
- 解析表格结构（保留样式和内容）
- 正则匹配题型标识（如[单选题]、[多选题]等）
- 图片转base64格式存储
- 表格转为{headers: [], rows: []}结构
- 支持多种题型：单选题、多选题、判断题、填空题、简答题、计算题等

## 使用方法

### 方法一：通过API接口调用

```typescript
// POST /word/parse
// 请求体
{
  "filePath": "本地Word文件路径"
}

// POST /word/upload
// 使用multipart/form-data上传Word文件
```

### 方法二：直接在代码中使用

```typescript
import { WordParserService } from '../service/word-parser.service';

// 创建服务实例
const wordParserService = new WordParserService();

// 解析Word文档
const result = await wordParserService.parseWordFile('文件路径.docx');

// 处理解析结果
if (result.success) {
  const questions = result.questions; // 试题数组
  console.log(`共解析出${questions.length}道题目`);
}
```

### 示例代码

可以参考 `src/example/word-parser-example.ts` 中的完整示例。

## 输出格式

解析结果为标准化的JSON数组，每个试题对象包含以下字段：

```typescript
interface QuestionOption {
  [key: string]: string;   // 选项结构为[{A: '选项内容'}, {B: '选项内容'}]
}

interface Question {
  type: string;       // 题型（单选题、多选题、判断题等）
  content: string;    // 题干内容
  options?: QuestionOption[]; // 选项（选择题），格式为 [{"A": "选项内容"}, {"B": "选项内容"}]
  answer?: string;    // 答案（单选题、多选题和判断题为纯文本，如"A"、"ABC"、"正确"）
  explanation?: string; // 解析
  images?: string[];  // 图片（Base64格式）
  tables?: Table[];   // 表格
  raw?: string;       // 原始内容
  knowledgePoints?: string[]; // 知识点（纯文本数组）
  difficulty?: string; // 难度（纯文本，如"简单"、"中等"、"困难"）
  level?: string;     // 题目分层（纯文本，如"基础"、"提高"、"拓展"）
  pageNumber?: number; // 页码
  answerArea?: number; // 答题区
}

interface Table {
  headers: string[];
  rows: string[][];
}
```

## 注意事项

1. 目前仅支持.docx格式文件，不支持.doc格式
2. 题型标识需要使用【题型】标记，如【题型】单选题
3. 题干内容需要使用【题干】标记
4. 答案和解析需要使用【答案】和【解析】标记
5. 选项格式需要符合规范，如A. 选项内容、B. 选项内容
6. 支持提取元数据，包括：
   - 【题目分层】：基础、提高、拓展等
   - 【难度】：简单、中等、困难等
   - 【知识点】：可以用逗号、顿号分隔多个知识点
   - 【页码】：数字
   - 【答题区】：数字
7. 对于复杂布局的文档，可能需要人工校对修正

## 技术实现

- 使用JSZip解压.docx文件，直接解析XML内容
- 使用xmldom解析Word文档的XML结构
- 使用JSDOM构建和操作HTML DOM
- 使用自定义算法将OMML格式的数学公式转换为LaTeX
- 使用正则表达式识别题型、选项、答案等
- 图片转Base64存储，表格结构化处理
- 作为备选方案，使用mammoth库进行基本解析

## 项目结构

项目采用模块化设计，各个功能模块位于`utils`目录下：

```
src/
├── service/                # 服务层
│   └── word-parser.service.ts  # 主解析服务
├── utils/                  # 工具类
│   ├── word-import/        # Word文档导入工具
│   │   ├── docx/           # Word文档解析工具
│   │   │   ├── index.ts    # 模块入口
│   │   │   ├── parser.ts   # 基础解析功能
│   │   │   ├── html-converter.ts # XML转HTML转换
│   │   │   └── README.md   # 模块说明
│   │   ├── formula/        # 数学公式处理工具
│   │   │   ├── docx-formula.ts # 公式处理
│   │   │   ├── latex-renderer.ts # 公式解析
│   │   │   └── README.md   # 模块说明
│   │   ├── image/          # 图片处理工具
│   │   │   ├── docx-image.ts # 图片处理
│   │   │   └── README.md   # 模块说明
│   │   ├── table/          # 表格处理工具
│   │   │   ├── docx-table.ts # 表格处理
│   │   │   └── README.md   # 模块说明
│   │   └── text/           # 文本处理工具
│   │       ├── index.ts    # 文本处理
│   │       └── README.md   # 模块说明
│   └── word-export/        # Word文档导出工具
│       ├── html-parser.ts  # HTML解析器
│       ├── image-processor.ts # 图片处理器
│       ├── style-parser.ts # 样式解析器
│       └── text-processor.ts # 文本处理器
└── index.ts                # 入口文件
```

每个模块都有自己的README文件，详细说明了模块的功能和使用方法。

## 后续优化方向

1. 提高解析准确率，支持更多题型和格式
2. 引入NLP技术辅助分析和分类
3. 支持批量导入和分布式处理
4. 提供可视化校对界面
5. 优化公式渲染，支持更复杂的数学表达式
6. 改进图片处理，支持更多图片格式和布局
7. 增强表格处理，支持合并单元格和复杂布局
8. 完善元数据提取，支持更多自定义字段
9. 优化代码结构，提高可维护性和可扩展性
10. 添加更多单元测试，提高代码质量和稳定性

# 题目导入服务

## 功能特性

- 支持从 Word 文档导入题目
- 支持将 HTML 内容导出为 Word 文档
- 自动清理临时文件
- 支持自定义页边距和页面方向
- 支持图片、表格、列表等复杂格式
- 支持文本样式（粗体、斜体、下划线、颜色等）
- 支持文本对齐方式
- 支持 SVG 图片转换

## 安装

```bash
npm install
```

## 快速验证

### 验证项目环境

```bash
# 安装依赖
npm install

# 编译项目
npm run build

# 运行测试（验证功能正常）
npm test
```

### 快速测试Word解析功能

#### 方法一：使用示例文件测试

您也可以使用以下命令直接运行 TypeScript 示例文件，无需先进行编译：

```bash
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx
```

或者先编译再运行：

```bash
npm run build
node dist/example/word-parser-example.js testFiles/input-word-sample.docx
```

#### 方法二：使用API接口测试

```bash
# 启动开发服务器
npm run dev

# 在另一个终端测试API（需要准备Word文件）
curl -X POST http://localhost:3131/word/parse \
  -H "Content-Type: application/json" \
  -d '{"filePath": "testFiles/input-word-sample.docx"}'
```

#### 方法三：使用文件上传测试

```bash
# 启动服务器
npm run dev

# 使用curl上传文件测试
curl -X POST http://localhost:3131/word/upload \
  -F "file=@testFiles/input-word-sample.docx"
```

### 快速测试Word导出功能

您也可以使用以下命令直接运行 TypeScript 示例文件，无需先进行编译：

```bash
node --require ts-node/register src/example/word-export-example.ts
```

或者先编译再运行：

```bash
npm run build
node dist/example/word-export-example.js
```

## 🧪 测试指南

### 📖 Word解析测试

**测试目标**: 将Word文档解析为结构化数据并生成预览

**源文件**: `testFiles/input-word-sample.docx`

**快速测试**:
```bash
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx
```

**生成文件**:
- `testFiles/input-word-sample_parsed.json` - JSON格式的解析结果
- `testFiles/input-word-sample_preview.html` - 可视化HTML预览文件

### 📄 Word导出测试

**测试目标**: 将HTML内容转换为Word文档

**源文件**: `testFiles/input-html-sample.html`

**快速测试**:
```bash
node --require ts-node/register src/example/word-export-example.ts
```

**生成文件**:
- `testFiles/output-word-result.docx` - 转换后的Word文档

### 🌐 API接口测试

**启动服务器**:
```bash
npm run dev
```

**测试解析API**:
```bash
curl -X POST http://localhost:3131/word/parse \
  -H "Content-Type: application/json" \
  -d '{"filePath": "testFiles/input-word-sample.docx"}'
```

**测试上传API**:
```bash
curl -X POST http://localhost:3131/word/upload \
  -F "file=@testFiles/input-word-sample.docx"
```

---

## 📝 命令参考

### TypeScript 直接执行命令

您也可以使用以下命令直接运行 TypeScript 文件，无需先进行编译：

#### Word解析示例
```bash
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx
```

#### Word导出示例
```bash
node --require ts-node/register src/example/word-export-example.ts
```

#### 运行测试
```bash
node --require ts-node/register node_modules/.bin/jest
```

#### 启动开发服务器
```bash
node --require ts-node/register src/bootstrap.ts
```

### 其他常用命令

#### 编译项目
```bash
npm run build
# 或者
npx tsc
```

#### 运行编译后的文件
```bash
node dist/example/word-export-example.js
node dist/example/word-parser-example.js testFiles/input-word-sample.docx
```

#### 清理编译文件
```bash
npm run clean
# 或者
rm -rf dist
```

### 验证输出结果

成功运行后，您应该看到：

1. **解析测试**：
   ```
   开始解析Word文件: testFiles/input-word-sample.docx
   解析成功，共提取 X 道题目
   解析结果已保存至: testFiles/input-word-sample_parsed.json
   HTML预览文件已保存至: testFiles/input-word-sample_preview.html
   ```

2. **导出测试**：
   ```
   开始执行Word导出示例...
   已读取HTML文件: testFiles/input-html-sample.html
   开始转换HTML到Word...
   Word文档保存成功: testFiles/output-word-result.docx
   ```

3. **API测试**：
   ```json
   {
     "success": true,
     "questions": [...],
     "message": "解析成功"
   }
   ```

### 常见问题排查

如果遇到问题，请按以下步骤排查：

#### Linux/macOS 用户

```bash
# 1. 检查Node.js版本（需要 >= 12.0.0）
node --version

# 2. 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 3. 检查TypeScript编译
npm run build

# 4. 运行单个测试文件
npx jest test/word-parser.test.ts

# 5. 查看详细错误信息
DEBUG=* npm run dev
```

#### Windows 用户

```cmd
# 1. 检查Node.js版本（需要 >= 12.0.0）
node --version

# 2. 清理并重新安装依赖
rmdir /s node_modules
del package-lock.json
npm install

# 3. 检查TypeScript编译
npm run build

# 4. 运行单个测试文件
npx jest test/word-parser.test.ts

# 5. 查看详细错误信息（PowerShell）
$env:DEBUG="*"; npm run dev
```

### 验证脚本

您也可以创建一个验证脚本来自动测试所有功能：

#### verify.js

```javascript
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始验证 Word 试题解析服务...\n');

try {
  // 1. 检查Node.js版本
  console.log('1️⃣ 检查Node.js版本...');
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`   Node.js版本: ${nodeVersion}`);

  // 2. 安装依赖
  console.log('\n2️⃣ 安装依赖...');
  execSync('npm install', { stdio: 'inherit' });

  // 3. 编译项目
  console.log('\n3️⃣ 编译项目...');
  execSync('npm run build', { stdio: 'inherit' });

  // 4. 运行测试
  console.log('\n4️⃣ 运行测试...');
  execSync('npm test', { stdio: 'inherit' });

  // 5. 测试Word导出功能
  console.log('\n5️⃣ 测试Word导出功能...');
  execSync('npx ts-node src/example/word-export-example.ts', { stdio: 'inherit' });

  // 6. 检查输出文件
  const outputFile = path.join(__dirname, 'examples', 'test-export-new.docx');
  if (fs.existsSync(outputFile)) {
    console.log('\n✅ 验证成功！所有功能正常工作。');
    console.log(`📄 生成的Word文档: ${outputFile}`);
  } else {
    console.log('\n❌ 验证失败：未找到输出文件');
  }

} catch (error) {
  console.error('\n❌ 验证过程中出现错误:');
  console.error(error.message);
  process.exit(1);
}
```

运行验证脚本：

```bash
# 创建并运行验证脚本
node verify.js
```

## 使用方法

### 导入 Word 文档

```typescript
import { WordImportService } from './src/service/word-import.service';

const wordImportService = new WordImportService();
const result = await wordImportService.importFromWord('path/to/file.docx');
```

### 导出 Word 文档

```typescript
import { WordExportService } from './src/service/word-export.service';

const wordExportService = new WordExportService();

// 从 HTML 内容导出
const htmlContent = '<div>Hello World</div>';
const buffer = await wordExportService.exportHtmlToWord(htmlContent, {
  title: '文档标题',
  author: '作者',
  margins: {
    top: 1440,    // 2.54厘米
    right: 1440,  // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440,   // 2.54厘米
  },
  orientation: 'portrait' // 或 'landscape'
});

// 从本地 HTML 文件导出
const filePath = await wordExportService.exportLocalHtmlToTemp('path/to/file.html', {
  title: '文档标题',
  author: '作者',
  margins: {
    top: 1440,
    right: 1440,
    bottom: 1440,
    left: 1440,
  },
  orientation: 'portrait'
});
```

## 支持的 HTML 特性

### 文本样式
- 粗体 (`<b>`, `<strong>`)
- 斜体 (`<i>`, `<em>`)
- 下划线 (`<u>`)
- 文本颜色
- 文本强调标记

### 段落格式
- 段落对齐（左对齐、居中、右对齐、两端对齐）
- 标题（H1-H6）
- 列表（有序列表、无序列表）
- 段落间距

### 表格
- 表格边框
- 单元格对齐（水平和垂直）
- 单元格宽度
- 单元格内容格式化

### 图片
- 支持 PNG、JPEG 等格式
- 支持 SVG 图片（自动转换为 PNG）
- 图片尺寸控制
- 图片对齐方式

### 布局
- 支持 flex 布局
- 支持 inline-block 布局
- 支持文本换行控制
- 支持边距设置

## 注意事项

1. 临时文件会自动清理，默认保留时间为 1 小时
2. 图片尺寸单位支持 px 和 ex
3. 颜色支持多种格式：
   - 十六进制（#RRGGBB）
   - RGB（rgb(r, g, b)）
   - RGBA（rgba(r, g, b, a)）
   - HSL（hsl(h, s%, l%)）
   - 颜色名称（black, white, red 等）

## 开发

```bash
# 安装依赖
npm install

# 运行测试
npm test

# 构建
npm run build
```

## 许可证

MIT

---

## 🚀 快速参考卡片

### 最常用命令

```bash
# 🔥 一键验证所有功能
node verify.js

# 📄 Word导出测试（最常用）
node --require ts-node/register src/example/word-export-example.ts

# 📖 Word解析测试
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx

# 🧪 运行测试
npm test

# 🚀 启动开发服务器
npm run dev
```

### 输出文件位置

- **Word导出结果**: `testFiles/output-word-result.docx`
- **Word解析JSON结果**: `testFiles/input-word-sample_parsed.json`
- **Word解析HTML预览**: `testFiles/input-word-sample_preview.html`
- **编译文件**: `dist/` 目录
- **日志文件**: `logs/` 目录

### 常见问题快速解决

```bash
# 🔧 重新安装依赖
rm -rf node_modules package-lock.json && npm install

# 🧹 清理编译文件
rm -rf dist && npm run build

# 🔍 查看详细错误
DEBUG=* npm run dev

# ✅ 验证环境
node --version && npm --version
```

---

**📖 更多详细信息请参考上方各章节内容**
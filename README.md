# Word试题解析服务使用说明

## 功能概述

本服务基于Midway.js框架开发，提供了强大的Word文档(.docx)解析功能，能够保留文档中的颜色、格式、图片、表格和数学公式等信息，并将其转换为HTML格式或标准化的JSON格式。主要功能包括：

- 解析.docx文件中的段落和样式，保留原始格式和颜色
- 处理数学公式（支持LaTeX渲染）
- 提取和处理图片（保留位置和大小信息）
- 解析表格结构（保留样式和内容）
- 正则匹配题型标识（如[单选题]、[多选题]等）
- 图片转base64格式存储
- 表格转为{headers: [], rows: []}结构
- 支持多种题型：单选题、多选题、判断题、填空题、简答题、计算题等

## 使用方法

### 方法一：通过API接口调用

```typescript
// POST /word/parse
// 请求体
{
  "filePath": "本地Word文件路径"
}

// POST /word/upload
// 使用multipart/form-data上传Word文件
```

### 方法二：直接在代码中使用

```typescript
import { WordParserService } from '../service/word-parser.service';

// 创建服务实例
const wordParserService = new WordParserService();

// 解析Word文档
const result = await wordParserService.parseWordFile('文件路径.docx');

// 处理解析结果
if (result.success) {
  const questions = result.questions; // 试题数组
  console.log(`共解析出${questions.length}道题目`);
}
```

### 示例代码

可以参考 `src/example/word-parser-example.ts` 中的完整示例。

## 输出格式

解析结果为标准化的JSON数组，每个试题对象包含以下字段：

```typescript
interface QuestionOption {
  [key: string]: string;   // 选项结构为[{A: '选项内容'}, {B: '选项内容'}]
}

interface Question {
  type: string;       // 题型（单选题、多选题、判断题等）
  content: string;    // 题干内容
  options?: QuestionOption[]; // 选项（选择题），格式为 [{"A": "选项内容"}, {"B": "选项内容"}]
  answer?: string;    // 答案（单选题、多选题和判断题为纯文本，如"A"、"ABC"、"正确"）
  explanation?: string; // 解析
  images?: string[];  // 图片（Base64格式）
  tables?: Table[];   // 表格
  raw?: string;       // 原始内容
  knowledgePoints?: string[]; // 知识点（纯文本数组）
  difficulty?: string; // 难度（纯文本，如"简单"、"中等"、"困难"）
  level?: string;     // 题目分层（纯文本，如"基础"、"提高"、"拓展"）
  pageNumber?: number; // 页码
  answerArea?: number; // 答题区
}

interface Table {
  headers: string[];
  rows: string[][];
}
```

## 注意事项

1. 目前仅支持.docx格式文件，不支持.doc格式
2. 题型标识需要使用【题型】标记，如【题型】单选题
3. 题干内容需要使用【题干】标记
4. 答案和解析需要使用【答案】和【解析】标记
5. 选项格式需要符合规范，如A. 选项内容、B. 选项内容
6. 支持提取元数据，包括：
   - 【题目分层】：基础、提高、拓展等
   - 【难度】：简单、中等、困难等
   - 【知识点】：可以用逗号、顿号分隔多个知识点
   - 【页码】：数字
   - 【答题区】：数字
7. 对于复杂布局的文档，可能需要人工校对修正

## 技术实现

- 使用JSZip解压.docx文件，直接解析XML内容
- 使用xmldom解析Word文档的XML结构
- 使用JSDOM构建和操作HTML DOM
- 使用自定义算法将OMML格式的数学公式转换为LaTeX
- 使用正则表达式识别题型、选项、答案等
- 图片转Base64存储，表格结构化处理
- 作为备选方案，使用mammoth库进行基本解析

## 项目结构

项目采用模块化设计，各个功能模块位于`utils`目录下：

```
src/
├── service/                # 服务层
│   └── word-parser.service.ts  # 主解析服务
├── utils/                  # 工具类
│   ├── docx/               # Word文档解析工具
│   │   ├── index.ts        # 模块入口
│   │   ├── parser.ts       # 基础解析功能
│   │   ├── html-converter.ts # XML转HTML转换
│   │   └── README.md       # 模块说明
│   ├── formula/            # 数学公式处理工具
│   │   ├── docx-formula.ts # 公式处理
│   │   ├── formula-parser.ts # 公式解析
│   │   └── README.md       # 模块说明
│   ├── image/              # 图片处理工具
│   │   ├── docx-image.ts   # 图片处理
│   │   └── README.md       # 模块说明
│   ├── table/              # 表格处理工具
│   │   ├── docx-table.ts   # 表格处理
│   │   └── README.md       # 模块说明
│   └── text/               # 文本处理工具
│       ├── index.ts        # 文本处理
│       └── README.md       # 模块说明
└── index.ts                # 入口文件
```

每个模块都有自己的README文件，详细说明了模块的功能和使用方法。

## 后续优化方向

1. 提高解析准确率，支持更多题型和格式
2. 引入NLP技术辅助分析和分类
3. 支持批量导入和分布式处理
4. 提供可视化校对界面
5. 优化公式渲染，支持更复杂的数学表达式
6. 改进图片处理，支持更多图片格式和布局
7. 增强表格处理，支持合并单元格和复杂布局
8. 完善元数据提取，支持更多自定义字段
9. 优化代码结构，提高可维护性和可扩展性
10. 添加更多单元测试，提高代码质量和稳定性

# 题目导入服务

## 功能特性

- 支持从 Word 文档导入题目
- 支持将 HTML 内容导出为 Word 文档
- 自动清理临时文件
- 支持自定义页边距和页面方向
- 支持图片、表格、列表等复杂格式
- 支持文本样式（粗体、斜体、下划线、颜色等）
- 支持文本对齐方式
- 支持 SVG 图片转换

## 安装

```bash
npm install
```

## 使用方法

### 导入 Word 文档

```typescript
import { WordImportService } from './src/service/word-import.service';

const wordImportService = new WordImportService();
const result = await wordImportService.importFromWord('path/to/file.docx');
```

### 导出 Word 文档

```typescript
import { WordExportService } from './src/service/word-export.service';

const wordExportService = new WordExportService();

// 从 HTML 内容导出
const htmlContent = '<div>Hello World</div>';
const buffer = await wordExportService.exportHtmlToWord(htmlContent, {
  title: '文档标题',
  author: '作者',
  margins: {
    top: 1440,    // 2.54厘米
    right: 1440,  // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440,   // 2.54厘米
  },
  orientation: 'portrait' // 或 'landscape'
});

// 从本地 HTML 文件导出
const filePath = await wordExportService.exportLocalHtmlToTemp('path/to/file.html', {
  title: '文档标题',
  author: '作者',
  margins: {
    top: 1440,
    right: 1440,
    bottom: 1440,
    left: 1440,
  },
  orientation: 'portrait'
});
```

## 支持的 HTML 特性

### 文本样式
- 粗体 (`<b>`, `<strong>`)
- 斜体 (`<i>`, `<em>`)
- 下划线 (`<u>`)
- 文本颜色
- 文本强调标记

### 段落格式
- 段落对齐（左对齐、居中、右对齐、两端对齐）
- 标题（H1-H6）
- 列表（有序列表、无序列表）
- 段落间距

### 表格
- 表格边框
- 单元格对齐（水平和垂直）
- 单元格宽度
- 单元格内容格式化

### 图片
- 支持 PNG、JPEG 等格式
- 支持 SVG 图片（自动转换为 PNG）
- 图片尺寸控制
- 图片对齐方式

### 布局
- 支持 flex 布局
- 支持 inline-block 布局
- 支持文本换行控制
- 支持边距设置

## 注意事项

1. 临时文件会自动清理，默认保留时间为 1 小时
2. 图片尺寸单位支持 px 和 ex
3. 颜色支持多种格式：
   - 十六进制（#RRGGBB）
   - RGB（rgb(r, g, b)）
   - RGBA（rgba(r, g, b, a)）
   - HSL（hsl(h, s%, l%)）
   - 颜色名称（black, white, red 等）

## 开发

```bash
# 安装依赖
npm install

# 运行测试
npm test

# 构建
npm run build
```

## 许可证

MIT
# Word导入工具 (word-import)

## 模块简介

`word-import` 模块专门用于解析Word文档(.docx)并转换为HTML格式，保留原始的格式、样式、图片、表格和数学公式等内容。

## 主要功能

- 解压并解析.docx文件
- 提取文档结构和内容
- 提取样式信息（颜色、字体、大小等）
- 处理图片关系和提取图片数据
- 处理数学公式（OMML转LaTeX）
- 处理表格结构和样式
- 将Word XML转换为HTML

## 目录结构

```
word-import/
├── docx/                # Word文档解析核心模块
│   ├── index.ts         # 模块主入口
│   ├── parser.ts        # XML解析和数据提取
│   ├── html-converter.ts # XML转HTML转换
│   └── README.md        # 模块说明文档
├── formula/             # 数学公式处理模块
│   ├── index.ts         # 模块主入口
│   ├── docx-formula.ts  # 公式HTML元素创建
│   ├── latex-renderer.ts # LaTeX渲染服务
│   ├── mathjax-formula.ts # MathJax公式处理
│   └── README.md        # 模块说明文档
├── image/               # 图片处理模块
│   ├── index.ts         # 模块主入口
│   ├── docx-image.ts    # Word图片处理
│   └── README.md        # 模块说明文档
├── table/               # 表格处理模块
│   ├── index.ts         # 模块主入口
│   ├── docx-table.ts    # Word表格处理
│   └── README.md        # 模块说明文档
├── text/                # 文本处理模块
│   ├── index.ts         # 模块主入口
│   └── README.md        # 模块说明文档
└── README.md            # 本文档
```

## 核心API

### 主入口函数

```typescript
import { parseDocxWithColors } from '../utils/word-import/docx';

/**
 * 解析Word文档，提取颜色信息
 * @param filePath Word文档路径
 * @returns HTML内容，保留颜色信息
 */
const html = await parseDocxWithColors('path/to/document.docx');
```

### 模块化使用

```typescript
// 单独使用各个模块
import { extractDocxXml, extractStyles } from '../utils/word-import/docx/parser';
import { convertToHtml } from '../utils/word-import/docx/html-converter';
import { processFormula } from '../utils/word-import/formula';
import { processDrawing } from '../utils/word-import/image/docx-image';
import { processTable } from '../utils/word-import/table/docx-table';
```

## 处理流程

1. **文档解压**: 使用JSZip解压.docx文件
2. **XML提取**: 提取document.xml、styles.xml等关键文件
3. **样式解析**: 解析样式信息并建立映射表
4. **图片处理**: 提取图片关系和数据，转换为base64格式
5. **内容转换**: 将Word XML结构转换为HTML DOM
6. **样式应用**: 应用文本样式、段落样式等
7. **特殊元素处理**: 处理公式、表格、图片等特殊元素
8. **HTML生成**: 生成最终的HTML内容

## 支持的Word元素

### 文本元素
- 段落 (`w:p`)
- 文本运行 (`w:r`)
- 文本内容 (`w:t`)
- 换行符 (`w:br`)
- 制表符 (`w:tab`)

### 样式元素
- 字体颜色
- 字体大小
- 粗体、斜体
- 下划线
- 着重号
- 段落对齐

### 结构元素
- 表格 (`w:tbl`)
- 图片 (`w:drawing`)
- 数学公式 (`m:oMath`)

## 技术特点

### 1. 样式保留
- 动态读取样式信息，避免硬编码
- 使用行内样式，便于后续处理
- 支持字符级别的样式应用

### 2. 公式处理
- 支持OMML到LaTeX转换
- 多种渲染方式（MathJax、在线服务）
- 自动降级处理，确保公式显示

### 3. 图片处理
- 支持多种图片格式
- 自动提取图片数据
- 保留图片尺寸和对齐信息

### 4. 表格处理
- 保留表格结构
- 支持单元格样式
- 处理合并单元格

## 使用示例

### 基本使用

```typescript
import { parseDocxWithColors } from '../utils/word-import/docx';
import * as fs from 'fs';

async function parseDocument() {
  try {
    // 解析Word文档
    const html = await parseDocxWithColors('testFiles/input-word-sample.docx');

    // 将结果保存到文件
    fs.writeFileSync('testFiles/output.html', html);

    console.log('文档解析成功，结果已保存到output.html');
  } catch (error) {
    console.error('解析文档失败:', error);
  }
}
```

### 高级使用

```typescript
import {
  extractDocxXml,
  extractStyles,
  extractImageRelations,
  extractImageData
} from '../utils/word-import/docx/parser';
import { convertToHtml } from '../utils/word-import/docx/html-converter';

async function advancedParse(filePath: string) {
  // 提取XML内容
  const { documentDoc, stylesDoc, relsDoc, zip } = await extractDocxXml(filePath);

  // 提取样式信息
  const styles = extractStyles(stylesDoc);

  // 提取图片数据
  let imageData = new Map<string, string>();
  if (relsDoc) {
    const imageRelations = extractImageRelations(relsDoc);
    imageData = await extractImageData(zip, imageRelations);
  }

  // 转换为HTML
  const html = await convertToHtml(documentDoc, styles, imageData);

  return html;
}
```

## 依赖项

- **JSZip**: 用于解压.docx文件
- **xmldom**: 用于解析XML
- **JSDOM**: 用于操作DOM
- **mammoth**: 用于公式转换（可选）

## 注意事项

1. **文件格式**: 仅支持.docx格式，不支持.doc格式
2. **复杂结构**: 对于复杂的文档结构，可能需要进一步优化
3. **公式渲染**: 公式处理支持多种模式，建议根据需求选择
4. **图片格式**: EMF和WMF格式可能无法直接显示
5. **性能考虑**: 大文件解析可能需要较长时间

## 扩展说明

如需添加新的Word元素支持，可以：

1. 在对应的模块中添加新的处理函数
2. 更新HTML转换器中的元素处理逻辑
3. 添加相应的样式映射
4. 更新测试用例和文档

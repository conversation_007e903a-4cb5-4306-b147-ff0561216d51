import { createApp, close } from '@midwayjs/mock';
import { Framework, Application } from '@midwayjs/koa';
import * as assert from 'assert';
import {
  NLPHelperService,
} from '../../src/service/nlp-helper.service';
import { QuestionType, Question } from '../../src/service/word-parser.service';

describe('nlp-helper.test.ts', () => {
  let app: Application;
  let nlpService: NLPHelperService;

  beforeAll(async () => {
    try {
      // 创建应用
      app = await createApp<Framework>();
      // 获取 NLPHelperService 实例
      nlpService = await app.getApplicationContext().getAsync(NLPHelperService);
    } catch (error) {
      console.error('Failed to create app:', error);
      throw error;
    }
  }, 30000); // 30秒超时

  afterAll(async () => {
    if (app) {
      await close(app);
    }
  }, 10000); // 10秒超时

  it('应该成功初始化NLP服务', async () => {
    await nlpService.init();
    // 初始化不应该抛出异常
    assert(true);
  });

  it('应该能够检测题型', async () => {
    // 模拟一个单选题
    const questionText =
      '下列哪个是三角函数？\nA. sin(x)\nB. log(x)\nC. exp(x)\nD. tan(y)';

    // 由于我们没有真实的API，这里会失败，但我们只测试函数是否正常运行
    const result = await nlpService.detectQuestionType(questionText);

    // 即使API调用失败，函数也应该返回一个有效的NLPResult对象
    assert(typeof result === 'object');
    assert('success' in result);
  });

  it('应该能够提取知识点', async () => {
    // 模拟一个包含知识点的题目
    const questionText = '请计算sin(30°)的值。';

    const result = await nlpService.extractKnowledgePoints(questionText);

    // 即使API调用失败，函数也应该返回一个有效的NLPResult对象
    assert(typeof result === 'object');
    assert('success' in result);
  });

  it('应该能够检测相似题目', async () => {
    // 模拟一个题目
    const question: Question = {
      type: QuestionType.SINGLE_CHOICE,
      content:
        '下列哪个是三角函数？\nA. sin(x)\nB. log(x)\nC. exp(x)\nD. tan(y)',
      options: ['sin(x)', 'log(x)', 'exp(x)', 'tan(y)'],
    };

    const result = await nlpService.detectSimilarQuestions(question, 0.8);

    // 即使API调用失败，函数也应该返回一个有效的NLPResult对象
    assert(typeof result === 'object');
    assert('success' in result);
  });

  it('应该能够批量处理题目', async () => {
    // 模拟题目列表
    const questions: Question[] = [
      {
        type: QuestionType.UNKNOWN,
        content:
          '下列哪个是三角函数？\nA. sin(x)\nB. log(x)\nC. exp(x)\nD. tan(y)',
        options: ['sin(x)', 'log(x)', 'exp(x)', 'tan(y)'],
      },
      {
        type: QuestionType.FILL_BLANK,
        content: '请计算sin(30°)的值。',
      },
    ];

    const result = await nlpService.batchProcessQuestions(questions);

    // 即使API调用失败，函数也应该返回一个有效的NLPResult对象
    assert(typeof result === 'object');
    assert('success' in result);
  });

  // 测试错误处理和重试机制需要模拟API调用，这里省略
});

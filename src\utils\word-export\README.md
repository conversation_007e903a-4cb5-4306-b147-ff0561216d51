# Word导出工具 (word-export)

## 模块简介

`word-export` 模块专门用于将HTML内容转换为Word文档(.docx)格式，支持文本、图片、表格、样式等内容的转换。

## 主要功能

- 解析HTML DOM结构
- 处理CSS样式并转换为Word样式
- 处理图片并转换为Word支持的格式
- 处理文本内容和格式
- 生成符合Word规范的文档结构

## 文件结构

```
word-export/
├── html-parser.ts      # HTML解析器，负责解析HTML DOM并转换为Word元素
├── image-processor.ts  # 图片处理器，处理HTML中的图片
├── style-parser.ts     # 样式解析器，解析CSS样式并转换为Word样式
├── text-processor.ts   # 文本处理器，处理文本内容和样式
└── README.md          # 本文档
```

## API说明

### HtmlParser 类

主要的HTML解析器，负责将HTML DOM转换为Word文档元素。

#### 主要方法

- `parseHtml(htmlContent: string): Document[]` - 解析HTML内容并返回Word文档元素数组
- `parseElement(element: Element): any` - 解析单个HTML元素
- `parseTable(table: Element): Table` - 解析HTML表格
- `parseParagraph(p: Element): Paragraph` - 解析HTML段落

### StyleParser 类

样式解析器，负责解析CSS样式并转换为Word支持的样式格式。

#### 主要方法

- `parseStyle(element: Element): StyleObject` - 解析元素的样式
- `rgbToHex(rgb: string): string` - 将RGB颜色转换为十六进制
- `mapTextEmphasisToDocx(emphasis: string): EmphasisMark` - 映射文本强调样式

### TextProcessor 类

文本处理器，负责处理文本内容和样式。

#### 主要方法

- `extractTextStyle(element: Element): TextStyle` - 提取文本样式
- `processTextContent(text: string): string` - 处理文本内容
- `applyTextStyle(run: Run, style: TextStyle): void` - 应用文本样式

### ImageProcessor 类

图片处理器，负责处理HTML中的图片。

#### 主要方法

- `processImage(img: Element): ImageRun` - 处理图片元素
- `convertImageToBuffer(src: string): Buffer` - 将图片转换为Buffer
- `getImageDimensions(img: Element): {width: number, height: number}` - 获取图片尺寸

## 使用示例

```typescript
import { HtmlParser } from '../utils/word-export/html-parser';
import { Document, Packer } from 'docx';

// 创建HTML解析器实例
const parser = new HtmlParser();

// 解析HTML内容
const htmlContent = '<p>Hello <b>World</b>!</p>';
const elements = parser.parseHtml(htmlContent);

// 创建Word文档
const doc = new Document({
  sections: [{
    children: elements
  }]
});

// 生成Word文档Buffer
const buffer = await Packer.toBuffer(doc);
```

## 支持的HTML元素

### 文本元素
- `<p>` - 段落
- `<h1>` - `<h6>` - 标题
- `<span>` - 行内文本
- `<strong>`, `<b>` - 粗体
- `<em>`, `<i>` - 斜体
- `<u>` - 下划线

### 列表元素
- `<ul>` - 无序列表
- `<ol>` - 有序列表
- `<li>` - 列表项

### 表格元素
- `<table>` - 表格
- `<tr>` - 表格行
- `<td>`, `<th>` - 表格单元格

### 图片元素
- `<img>` - 图片（支持base64和URL）

## 支持的CSS样式

### 文本样式
- `color` - 文字颜色
- `font-weight` - 字体粗细
- `font-style` - 字体样式（斜体）
- `text-decoration` - 文本装饰（下划线）
- `text-align` - 文本对齐
- `font-size` - 字体大小

### 布局样式
- `width` - 宽度
- `height` - 高度
- `margin` - 外边距
- `padding` - 内边距

### 特殊样式
- `text-emphasis` - 文本强调（着重号）
- `vertical-align` - 垂直对齐

## 注意事项

1. **样式转换**: CSS样式会尽可能转换为Word支持的格式，不支持的样式会被忽略
2. **图片处理**: 支持base64格式的图片，外部URL图片需要先下载
3. **表格支持**: 支持基本的表格结构，复杂的表格布局可能需要调整
4. **字体支持**: 建议使用常见的字体，特殊字体可能在不同系统上显示不一致

## 扩展说明

如需添加新的HTML元素或CSS样式支持，可以：

1. 在对应的处理器中添加新的解析方法
2. 更新样式映射表
3. 添加相应的测试用例
4. 更新文档说明

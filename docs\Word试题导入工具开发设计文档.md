Word试题导入工具开发设计文档  
版本：1.0  
日期：2025-04-25  
框架：Midway.js (Node.js)  
 
---
 
1. 项目概述  
1.1 目标  
开发一个基于Midway.js的Word试题导入工具，支持批量解析Word文档（.docx），提取试题（选择题、填空题、判断题等），并存储到数据库。  
 
1.2 核心功能  
- Word文档解析：支持文本、图片、表格、公式等内容的提取。  
- 试题结构化存储：按题型分类存储到数据库（MySQL/MongoDB）。  
- NLP辅助分析（可选）：自动分类、知识点打标、相似题检测。  
- 可视化校对：提供管理界面，人工修正解析错误。  
 
---
 
2. 技术选型

| 模块 | 技术栈 | 依赖包 |  
|------|--------|--------|  
| 后端框架 | Midway.js 3.0 | `@midwayjs/core`, `@midwayjs/koa` |  
| Word解析 | docx.js / officegen | `docx`, `mammoth` |  
| 数据库 | MySQL / MongoDB | `typeorm` / `mongoose` |  
| NLP增强 | 阿里云NLP API / HuggingFace | `@alicloud/nlp-api` |  
| 任务队列 | BullMQ（大文件处理） | `bullmq` |  
| 日志监控 | Winston + Sentry | `winston`, `@sentry/node` |  
 
---
 
3. 系统架构  
3.1 流程图  
```mermaid 
graph TD 
    A[上传Word文件] --> B[解析文档]
    B --> C{是否包含图片/表格?}
    C -->|是| D[提取图片/表格]
    C -->|否| E[提取纯文本试题]
    D --> F[结构化存储]
    E --> F 
    F --> G[数据库存储]
    G --> H[管理端校对]
```
 
3.2 核心模块  
1. Word解析模块  
   - 使用`docx.js`解析.docx文件，提取段落、表格、图片。  
   - 正则匹配题型（如`[单选题]`、`[判断题]`）。  
   - 图片转Base64存储，表格转JSON。  
 
2. 试题存储模块  
   - 定义数据库Schema（题型、题干、选项、答案、解析）。  
   - 支持批量导入，避免重复试题。  
 
3. NLP辅助模块（可选）  
   - 调用NLP API进行题型分类（如识别“简述题”vs“填空题”）。  
   - 关键词提取（如“三角函数”、“牛顿定律”）。  
 
4. 管理端校对模块  
   - 提供Web界面，人工修正解析错误。  
   - 支持重新映射题型、修正选项错位。  
 
--- 
 
4. 关键实现逻辑  
4.1 Word解析策略  
- 文本试题：按行解析，匹配`1.`、`A.`等题号格式。  
- 图片/公式：提取为Base64，存储到OSS/CDN，数据库存URL。  
- 表格试题：转为JSON结构，例如：  
  ```json 
  {
    "type": "表格题",
    "content": [
      ["问题1", "选项A", "选项B"],
      ["问题2", "选项A", "选项B"]
    ]
  }
  ```
 
4.2 异常处理  
- 版本兼容：强制`.docx`格式，旧版`.doc`用`libreoffice`转换。  
- 复杂布局：保留原始HTML片段，人工校对修正。  
- 性能优化：超过10MB文件分片解析，用`Worker`线程处理。  
 
---
 
5. 迭代计划  

| 阶段 | 目标 | 周期 |
|------|------|------|  
| V1.0 | 基础文本解析（选择题/判断题） | 2周 |  
| V2.0 | 支持图片/表格 + 错误修正系统 | 3周 |  
| V3.0 | NLP分类 + 分布式任务队列 | 4周 |  
 
---
 
6. 风险与应对  
- 解析准确率不足：初期依赖人工校对，积累样本优化规则。  
- NLP成本高：前期用规则引擎，后期再引入AI模型。  
- 大文件崩溃：内存限制+分片解析，超时任务自动重试。  
 
---
 
7. 后续扩展  
- 支持Excel/PDF导入：集成`pdf-lib`或`exceljs`。  
- 自动组卷功能：根据知识点随机抽题生成试卷。  
- 多端同步：小程序/Web端题库管理。  
 
---
 
负责人： zpl  
审核人： zpl  
GitHub仓库： https://e.coding.net/xianyunshipei/zuoyesheji/question-import-word.git  
 
--- 
 
附录  
-   [Midway官方文档](https://midwayjs.org/)
-   [docx.js示例代码](https://github.com/dolanmiu/docx)
-   [阿里云NLP API文档](https://help.aliyun.com/product/94590.html)
 
---
import { MidwayConfig } from '@midwayjs/core';

export default {
  // 测试环境使用不同的端口避免冲突
  koa: {
    port: 0, // 使用随机端口
  },

  // 测试环境禁用一些不必要的功能
  bullmq: {
    defaultConnection: {
      host: '127.0.0.1',
      port: 6379,
      // 测试环境可以不需要密码
    },
    defaultPrefix: '{test-midway-bullmq}',
  },

  // 测试环境Redis配置
  redis: {
    client: {
      port: 6379,
      host: '127.0.0.1',
      db: 1, // 使用不同的数据库
    },
  },

  // 测试环境日志配置
  midwayLogger: {
    default: {
      level: 'warn', // 测试时减少日志输出
    },
  },
} as MidwayConfig;

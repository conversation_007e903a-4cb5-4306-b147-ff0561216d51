import { Provide } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';
import { JSDOM } from 'jsdom';
import {
  Document,
  Packer,
  Paragraph,
  Run,
  ImageRun,
  Table,
  TableRow,
  TableCell,
  HeadingLevel,
  SectionType,
} from 'docx';
import { HtmlParser } from './word-export/utils/html-parser';
const sharp = require('sharp');

/**
 * Word导出服务
 * 提供将HTML内容转换为Word文档的功能
 */
@Provide()
export class WordExportService {
  private readonly NODE_TYPES = {
    ELEMENT_NODE: 1,
    TEXT_NODE: 3,
  };

  private readonly OUTPUT_DIR = path.join(
    process.cwd(),
    'temp',
    'word-exports'
  );

  // 默认页边距（单位：缇，1厘米 = 567缇）
  private readonly DEFAULT_MARGINS = {
    top: 1440, // 2.54厘米
    right: 1440, // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440, // 2.54厘米
  };

  private readonly DEFAULT_PAGE_WIDTH_CM = 21; // A4纸宽度，单位：cm
  private readonly CM_TO_PX = 37.8; // 1cm = 37.8px

  private calculatePageWidth(margins: {
    left?: number;
    right?: number;
  }): number {
    // 页边距单位为 twip，需转为 cm
    const leftMargin = margins.left ?? this.DEFAULT_MARGINS.left;
    const rightMargin = margins.right ?? this.DEFAULT_MARGINS.right;
    // 页边距总和（cm）
    const marginCm = (leftMargin + rightMargin) / 567;
    // 最大可用宽度（cm）
    const maxWidthCm = this.DEFAULT_PAGE_WIDTH_CM - marginCm;
    // 返回像素值
    return maxWidthCm * this.CM_TO_PX;
  }

  constructor() {
    // 确保输出目录存在
    if (!fs.existsSync(this.OUTPUT_DIR)) {
      fs.mkdirSync(this.OUTPUT_DIR, { recursive: true });
    }
  }

  /**
   * 将HTML内容转换为Word文档
   * @param htmlContent HTML内容
   * @param options 导出选项
   * @returns Word文档的Buffer
   */
  async exportHtmlToWord(
    htmlContent: string,
    options: {
      title?: string;
      author?: string;
      fileName?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
    } = {}
  ): Promise<Buffer> {
    try {
      const dom = new JSDOM(htmlContent);
      const { document } = dom.window;

      // 合并默认页边距和用户设置的页边距
      const margins = {
        ...this.DEFAULT_MARGINS,
        ...options.margins,
      };

      const htmlParser = new HtmlParser();
      const children = await htmlParser.parseHtmlNodes(
        document.body.childNodes,
        margins
      );

      const doc = new Document({
        sections: [
          {
            properties: {
              type: SectionType.CONTINUOUS,
              page: {
                margin: {
                  top: margins.top,
                  right: margins.right,
                  bottom: margins.bottom,
                  left: margins.left,
                },
                size: {
                  orientation:
                    options.orientation === 'landscape'
                      ? 'landscape'
                      : 'portrait',
                },
              },
            },
            children,
          },
        ],
        styles: {
          default: {
            document: {
              run: {
                font: '宋体',
                size: 24, // 12pt
              },
              paragraph: {
                spacing: {
                  line: 360, // 1.5倍行距
                },
              },
            },
          },
        },
      });

      return await Packer.toBuffer(doc);
    } catch (error) {
      console.error('导出Word文档失败:', error);
      throw error;
    }
  }

  /**
   * 从本地HTML文件导出Word文档到临时目录
   * @param htmlFilePath HTML文件路径
   * @param options 导出选项
   * @returns 生成的Word文件路径
   */
  async exportLocalHtmlToTemp(
    htmlFilePath: string,
    options: {
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
    } = {}
  ): Promise<string> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(htmlFilePath)) {
        throw new Error(`HTML文件不存在: ${htmlFilePath}`);
      }

      // 读取HTML文件内容
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');

      // 生成唯一的输出文件名
      const outputFileName = this.generateUniqueFileName();
      const outputPath = path.join(this.OUTPUT_DIR, outputFileName);

      // 合并默认页边距和用户设置的页边距
      const exportOptions = {
        ...options,
        margins: {
          ...this.DEFAULT_MARGINS,
          ...options.margins,
        },
      };

      // 导出Word文档
      const result = await this.exportHtmlToWordFile(
        htmlContent,
        outputPath,
        exportOptions
      );

      return result;
    } catch (error) {
      console.error('从本地HTML文件导出Word文档失败:', error);
      throw error;
    }
  }

  /**
   * 生成唯一的文件名
   */
  private generateUniqueFileName(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `export_${timestamp}_${random}.docx`;
  }

  /**
   * 将HTML内容转换为Word文档并保存到文件
   * @param htmlContent HTML内容
   * @param outputPath 输出文件路径
   * @param options 导出选项
   * @returns 保存的文件路径
   */
  async exportHtmlToWordFile(
    htmlContent: string,
    outputPath: string,
    options: {
      title?: string;
      author?: string;
      fileName?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
    } = {}
  ): Promise<string> {
    try {
      const docxBuffer = await this.exportHtmlToWord(htmlContent, options);
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      await fs.promises.writeFile(outputPath, docxBuffer);
      return outputPath;
    } catch (error) {
      console.error('保存Word文档失败:', error);
      throw error;
    }
  }

  private async svgBase64ToPngBuffer(
    svgData: string,
    width: number,
    height: number
  ): Promise<Buffer> {
    let svgBuffer: Buffer;
    if (/;base64,/.test(svgData)) {
      const base64 = svgData.split(';base64,').pop()!;
      svgBuffer = Buffer.from(base64, 'base64');
    } else {
      const svgText = decodeURIComponent(svgData.split(',').pop()!);
      svgBuffer = Buffer.from(svgText);
    }
    // 输出更高分辨率，白色背景去黑边
    return await sharp(svgBuffer)
      .resize(Math.round(width * 3), Math.round(height * 3), {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      })
      .png()
      .toBuffer();
  }

  private async parseRuns(
    nodeList: NodeList,
    parentStyle: any = {},
    pageWidth?: number // 添加页面宽度参数
  ): Promise<Run[]> {
    const runs: Run[] = [];
    for (let i = 0; i < nodeList.length; i++) {
      const node = nodeList[i];
      if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          // Use the color and emphasis from the nearest parent element with a style, or the inherited parentStyle
          let currentColor = parentStyle.color;
          let currentEmphasis = parentStyle.emphasisMark;
          let currentNode = node.parentNode as Element;
          while (
            currentNode &&
            currentNode.nodeType === this.NODE_TYPES.ELEMENT_NODE
          ) {
            const elementStyle = this.parseStyle(currentNode);
            if (elementStyle.color !== undefined) {
              currentColor = elementStyle.color;
            }
            if (
              elementStyle.textEmphasis !== undefined ||
              elementStyle.textEmphasisPosition !== undefined
            ) {
              currentEmphasis = this.mapTextEmphasisToDocx(
                elementStyle.textEmphasis,
                elementStyle.textEmphasisPosition
              );
            }
            // If we found both color and emphasis on this node, we can stop searching up
            if (
              elementStyle.color !== undefined &&
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined)
            )
              break;
            if (
              elementStyle.color !== undefined &&
              parentStyle.color === undefined
            )
              break; // Stop if a color is found and parent didn't have one
            if (
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined) &&
              parentStyle.emphasisMark === undefined
            )
              break; // Stop if emphasis is found and parent didn't have one

            currentNode = currentNode.parentNode as Element;
          }

          const run = new Run({
            text,
            bold: parentStyle.bold,
            italics: parentStyle.italics,
            underline: parentStyle.underline,
            color: currentColor !== undefined ? currentColor : '000000',
            emphasisMark: currentEmphasis, // Apply emphasis mark
          });
          runs.push(run);
        } else {
          const rawText = node.textContent ?? '';
          const pure = rawText.replace(/[\s\u3000]/g, '');
          // 纯空格文本
          if (rawText && pure.length === 0) {
            //从当前节点往上直到遇到DIV前的所有父节点tagName数组
            const parentTags = this.getParentTagNamesUntilDiv(node);
            // 父节点中包含有U标签 转换为下划线
            if (parentTags.length && parentTags.includes('U')) {
              // 生成等长下划线
              runs.push(
                new Run({ text: rawText, underline: { type: 'single' } })
              );
              continue;
            }
          }
        }
      } else if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;
        if (el.tagName === 'IMG') {
          const style = this.parseStyle(el);
          const src = el.getAttribute('src');
          if (src && src.startsWith('data:image/')) {
            if (src.startsWith('data:image/svg+xml')) {
              // 自动识别SVG尺寸
              let width =
                style.width || parseInt(el.getAttribute('width') || '0', 10);
              let height =
                style.height || parseInt(el.getAttribute('height') || '0', 10);
              if (!width || !height) {
                // 解析SVG源码
                let svgText = '';
                if (/;base64,/.test(src)) {
                  const base64 = src.split(';base64,').pop()!;
                  svgText = Buffer.from(base64, 'base64').toString();
                } else {
                  svgText = decodeURIComponent(src.split(',').pop()!);
                }
                const size = this.getSvgIntrinsicSize(svgText);
                if (size) {
                  if (!width) width = size.width;
                  if (!height) height = size.height;
                }
              }
              // 兜底默认值
              if (!width) width = 120;
              if (!height) height = 40;

              // 限制图片宽度不超过页面宽度
              if (pageWidth && width > pageWidth) {
                const ratio = pageWidth / width;
                width = Math.round(width * ratio);
                height = Math.round(height * ratio);
              }

              const buffer = await this.svgBase64ToPngBuffer(
                src,
                width,
                height
              );

              // 处理图片对齐方式
              const imageRunProps: any = {
                data: buffer,
                transformation: { width, height },
                type: 'png',
                verticalAlign: 'middle', // 确保垂直居中对齐
              };

              // 获取父元素的样式（可能包含垂直对齐信息，但我们优先使用middle）
              const parentElement = el.parentElement;

              // 处理图片容器的样式
              if (parentElement) {
                const parentStyle = this.parseStyle(parentElement);
                if (parentStyle.display === 'inline-block') {
                  // 设置图片的边距
                  if (parentStyle.margin) {
                    const margins = parentStyle.margin.split(' ');
                    if (margins.length === 4) {
                      imageRunProps.margins = {
                        top: parseInt(margins[0]),
                        right: parseInt(margins[1]),
                        bottom: parseInt(margins[2]),
                        left: parseInt(margins[3]),
                      };
                    }
                  }
                }
              }

              runs.push(new ImageRun(imageRunProps));
            } else {
              const base64Data = src.split(';base64,').pop();
              if (base64Data) {
                const buffer = Buffer.from(base64Data, 'base64');

                // 尝试从HTML属性获取尺寸
                let width =
                  style.width || parseInt(el.getAttribute('width') || '0', 10);
                let height =
                  style.height ||
                  parseInt(el.getAttribute('height') || '0', 10);

                // 如果未从HTML属性获取到尺寸，则从图片buffer中获取
                if (width <= 0 || height <= 0) {
                  try {
                    const metadata = await sharp(buffer).metadata();
                    width = metadata.width || width;
                    height = metadata.height || height;
                  } catch (e) {
                    // 兜底默认值，避免宽度或高度为0
                    if (width <= 0) width = 100;
                    if (height <= 0) height = 100;
                  }
                }

                // 限制图片宽度不超过页面宽度
                if (pageWidth && width > pageWidth) {
                  const ratio = pageWidth / width;
                  width = Math.round(width * ratio);
                  height = Math.round(height * ratio);
                }

                // 处理图片对齐方式
                const imageRunProps: any = {
                  data: buffer,
                  transformation: { width, height },
                  type: 'png',
                  verticalAlign: 'middle', // 确保垂直居中对齐
                };

                // 获取父元素的样式（可能包含垂直对齐信息，但我们优先使用middle）
                const parentElement = el.parentElement;

                // 处理图片容器的样式
                if (parentElement) {
                  const parentStyle = this.parseStyle(parentElement);
                  if (parentStyle.display === 'inline-block') {
                    // 设置图片的边距
                    if (parentStyle.margin) {
                      const margins = parentStyle.margin.split(' ');
                      if (margins.length === 4) {
                        imageRunProps.margins = {
                          top: parseInt(margins[0]),
                          right: parseInt(margins[1]),
                          bottom: parseInt(margins[2]),
                          left: parseInt(margins[3]),
                        };
                      }
                    }
                  }
                }

                runs.push(new ImageRun(imageRunProps));
              }
            }
          }
        } else {
          const style = this.extractTextStyle(el, parentStyle);
          // Ensure the alignment from extractTextStyle doesn't override paragraph alignment in runs
          // Alignment is a paragraph-level property in docx, so it should not be set on individual runs.
          // We should remove alignment from the style passed to parseRuns.
          const styleForRuns = { ...style, alignment: undefined }; // Remove alignment property
          const childRuns = await this.parseRuns(
            el.childNodes,
            styleForRuns,
            pageWidth
          ); // 传递页面宽度
          runs.push(...childRuns);
        }
      }
    }
    return runs;
  }
  /**
   * 向上递归查找父节点（第一个DIV时停止）
   * @param node 节点
   * @returns 父节点数组
   */
  private getParentTagNamesUntilDiv(node: Node): string[] {
    const tagNames: string[] = [];
    let current = node.parentNode as Element | null;
    while (current && current.tagName !== 'DIV') {
      tagNames.push(current.tagName);
      current = current.parentNode as Element | null;
    }
    return tagNames;
  }
  private async parseParagraph(el: Element): Promise<Paragraph> {
    const style = this.extractTextStyle(el);
    const runs: Run[] = await this.parseRuns(el.childNodes, style);
    let alignment;
    if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      // 兼容 align 属性
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }
    return new Paragraph({ children: runs, alignment, spacing: { line: 360 } });
  }

  private async parseHeading(el: Element): Promise<Paragraph> {
    // Extract style for the heading element itself, inheriting from its parent
    const parentElement = el.parentElement;
    const parentStyle = parentElement
      ? this.extractTextStyle(parentElement, {})
      : {}; // Use extractTextStyle to get inherited styles
    const style = this.extractTextStyle(el, parentStyle); // Get the combined style for the heading element

    // Parse runs within the heading element, using the combined style for inheritance
    const runs: Run[] = await this.parseRuns(el.childNodes, style); // Pass the combined style

    const level = parseInt(el.tagName.substring(1), 10);
    let heading:
      | 'Heading1'
      | 'Heading2'
      | 'Heading3'
      | 'Heading4'
      | 'Heading5'
      | 'Heading6'
      | 'Title' = HeadingLevel.HEADING_1;
    switch (level) {
      case 1:
        heading = HeadingLevel.HEADING_1;
        break;
      case 2:
        heading = HeadingLevel.HEADING_2;
        break;
      case 3:
        heading = HeadingLevel.HEADING_3;
        break;
      case 4:
        heading = HeadingLevel.HEADING_4;
        break;
      case 5:
        heading = HeadingLevel.HEADING_5;
        break;
      case 6:
        heading = HeadingLevel.HEADING_6;
        break;
    }

    // Determine alignment: prioritize element's style, then inherited parent style, then default
    let alignment: any; // Use any to avoid strict type issues with Paragraph alignment
    if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      // 兼容 align 属性
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }
    // If no explicit alignment on element or inherited, use undefined which defaults to left in docx

    // Add spacing after the heading
    const spacingAfter = 200; // Example: Add 10pt spacing after (1pt = 20 twips)

    return new Paragraph({
      children: runs,
      heading,
      alignment,
      spacing: { after: spacingAfter, line: 360 }, // 在 existing spacing object 中添加 line
    });
  }

  private async parseHtmlNodes(
    nodeList: NodeList,
    margins?: { left?: number; right?: number }
  ): Promise<(Paragraph | Table)[]> {
    const result: (Paragraph | Table)[] = [];
    let currentParagraph: Paragraph | null = null;

    // 计算页面可用宽度
    const pageWidth = this.calculatePageWidth(margins || {});

    // Helper function to finalize the current paragraph
    const finalizeCurrentParagraph = () => {
      if (currentParagraph) {
        result.push(currentParagraph);
        currentParagraph = null;
      }
    };

    for (const node of Array.from(nodeList)) {
      if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;
        const parentElement = el.parentElement;
        const parentStyle = parentElement
          ? this.extractTextStyle(parentElement)
          : {};
        const style = this.parseStyle(el);

        // 块级元素标签 (P, TABLE, H1-H6, UL, OL)
        const isStandardBlockTag =
          el.tagName === 'P' ||
          el.tagName === 'TABLE' ||
          el.tagName.match(/^H[1-6]$/) ||
          el.tagName === 'UL' ||
          el.tagName === 'OL';

        // Check if the element or its parent has flex or inline-flex display
        const isCurrentElementFlexContainer =
          style.display === 'flex' || style.display === 'inline-flex';
        const isParentFlexContainer =
          parentStyle.display === 'flex' ||
          parentStyle.display === 'inline-flex';

        // Check if the element is typically an inline element or its display is inline
        const isInlineElement =
          el.tagName === 'SPAN' ||
          el.tagName === 'A' ||
          el.tagName === 'IMG' ||
          el.tagName === 'B' ||
          el.tagName === 'STRONG' ||
          el.tagName === 'I' ||
          el.tagName === 'EM' ||
          el.tagName === 'U' ||
          style.display === 'inline';

        // Check for specific DIV patterns that should act as blocks (like question/option containers or text-heavy divs)
        const isQuestionOrOptionContainer =
          el.tagName === 'DIV' &&
          (style.alignItems === 'center' ||
            parentStyle.alignItems === 'center') &&
          (style.whiteSpace === 'nowrap' ||
            parentStyle.whiteSpace === 'nowrap');

        // Check if a DIV contains text nodes directly or primarily inline/text-like content, and is not a flex container
        const containsDirectTextNode = Array.from(el.childNodes).some(
          child =>
            child.nodeType === this.NODE_TYPES.TEXT_NODE &&
            child.textContent?.trim()
        );

        const containsParagraphLikeContent =
          containsDirectTextNode ||
          Array.from(el.children).some(childEl => {
            const childStyle = this.parseStyle(childEl as Element);
            return (
              (childEl as Element).tagName === 'SPAN' ||
              (childEl as Element).tagName === 'B' ||
              (childEl as Element).tagName === 'STRONG' ||
              (childEl as Element).tagName === 'I' ||
              (childEl as Element).tagName === 'EM' ||
              (childEl as Element).tagName === 'U' ||
              (childEl as Element).tagName === 'A' ||
              (childEl as Element).tagName === 'IMG' ||
              childStyle.display === 'inline' ||
              childStyle.display === 'inline-block' // Consider inline-block children as part of paragraph-like content
            );
          });

        const isTextContainingDivBlock =
          el.tagName === 'DIV' &&
          !isCurrentElementFlexContainer && // Not a flex container itself
          style.display !== 'inline' &&
          style.display !== 'inline-block' && // Not inline or inline-block itself
          !isQuestionOrOptionContainer && // Not already handled as a specific container
          containsParagraphLikeContent; // Contains text or inline/inline-block content

        if (isStandardBlockTag) {
          // Standard block tags always start a new block
          finalizeCurrentParagraph();

          if (el.tagName === 'P') {
            result.push(await this.parseParagraph(el));
          } else if (el.tagName === 'TABLE') {
            result.push(await this.parseTable(el));
          } else if (el.tagName.match(/^H[1-6]$/)) {
            result.push(await this.parseHeading(el));
          } else if (el.tagName === 'UL' || el.tagName === 'OL') {
            const numberingReference =
              el.tagName === 'UL' ? 'my-unordered-list' : 'my-ordered-list';
            const listParagraphs = await this.parseList(el, numberingReference);
            result.push(...listParagraphs);
          }
        } else if (isQuestionOrOptionContainer || isTextContainingDivBlock) {
          // These specific DIV patterns or text-containing divs also start a new block/paragraph
          finalizeCurrentParagraph();
          // Process children as runs within a new paragraph
          const runs = await this.parseRuns(
            el.childNodes,
            this.extractTextStyle(el, parentStyle),
            pageWidth
          );
          if (runs.length > 0) {
            const combinedStyle = this.extractTextStyle(el, parentStyle);
            let paragraphAlignment: any = 'left'; // Default alignment
            if (
              combinedStyle.alignment &&
              ['left', 'center', 'right', 'justify'].includes(
                combinedStyle.alignment
              )
            ) {
              paragraphAlignment = combinedStyle.alignment;
            }
            result.push(
              new Paragraph({
                children: runs,
                alignment: paragraphAlignment,
                spacing: { line: 360 },
              })
            );
          }
        } else if (
          isInlineElement ||
          isParentFlexContainer ||
          isCurrentElementFlexContainer
        ) {
          // If it's an inline element, or within/is a flex container, treat content as inline flow

          // Ensure there is a current paragraph to add runs to
          if (!currentParagraph) {
            // Inherit alignment from parent for the new paragraph
            const inheritedStyle = this.extractTextStyle(el, parentStyle);
            let paragraphAlignment: any = 'left'; // Default alignment
            if (
              inheritedStyle.alignment &&
              ['left', 'center', 'right', 'justify'].includes(
                inheritedStyle.alignment
              )
            ) {
              paragraphAlignment = inheritedStyle.alignment;
            }
            currentParagraph = new Paragraph({
              alignment: paragraphAlignment,
              spacing: { line: 360 },
            });
            result.push(currentParagraph);
          }

          // Parse children as runs and add to the current paragraph
          const styleForRuns = this.extractTextStyle(el, parentStyle);
          const childRuns = await this.parseRuns(
            el.childNodes,
            styleForRuns,
            pageWidth
          );

          if (currentParagraph && (currentParagraph as any)._children) {
            (currentParagraph as any)._children.push(...childRuns);
          }
        } else {
          // For all other elements (like generic DIVs that don't fit the above categories),
          // recursively process their children and add the results.

          // Finalize current paragraph before processing children as potentially new blocks
          finalizeCurrentParagraph();

          const children = await this.parseHtmlNodes(el.childNodes, margins);
          result.push(...children);
        }
      } else if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          // Text nodes always contribute to the current paragraph
          if (!currentParagraph) {
            // If no current paragraph, create one with default alignment
            currentParagraph = new Paragraph({ spacing: { line: 360 } });
            result.push(currentParagraph);
          }
          if (currentParagraph && (currentParagraph as any)._children) {
            (currentParagraph as any)._children.push(new Run({ text }));
          }
        }
      } else if (
        node.nodeType === (this.NODE_TYPES as any).DOCUMENT_FRAGMENT_NODE
      ) {
        // If it's a DocumentFragment (can happen with innerHTML/outerHTML parsing),
        // recursively process its children.
        const children = await this.parseHtmlNodes(node.childNodes, margins);
        result.push(...children);
      }
      // Ignore other node types (comments, etc.)
    }

    // After the loop, finalize any remaining current paragraph
    finalizeCurrentParagraph();

    return result;
  }

  // Fix parseList to accept and use numberingReference
  private async parseList(
    el: Element,
    numberingReference: string // Added numberingReference parameter
  ): Promise<Paragraph[]> {
    const result: Paragraph[] = [];
    for (const li of Array.from(el.querySelectorAll('li'))) {
      const runs = await this.parseRuns(li.childNodes);
      // Apply the numbering reference and level to the paragraph
      result.push(
        new Paragraph({
          children: runs,
          numbering: { reference: numberingReference, level: 0 }, // Use numberingReference
          spacing: { line: 360 },
        })
      );
    }
    return result;
  }

  private extractTextStyle(el: Element, parentStyle: any = {}): any {
    const style = this.parseStyle(el);
    // 解析 font-weight
    let fontWeight = style.fontWeight || el.getAttribute('font-weight');
    if (!fontWeight && el.hasAttribute('style')) {
      const fwMatch = el.getAttribute('style')?.match(/font-weight:\s*([^;]+)/);
      if (fwMatch) fontWeight = fwMatch[1];
    }
    // 解析 font-style
    let fontStyle = style.fontStyle || el.getAttribute('font-style');
    if (!fontStyle && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-style:\s*([^;]+)/);
      if (fsMatch) fontStyle = fsMatch[1];
    }
    // 解析 text-align
    let textAlign = style.textAlign;
    if (!textAlign && el.hasAttribute('style')) {
      const taMatch = el.getAttribute('style')?.match(/text-align:\s*([^;]+)/);
      if (taMatch) textAlign = taMatch[1];
    }
    return {
      bold:
        parentStyle.bold ||
        el.tagName === 'B' ||
        el.tagName === 'STRONG' ||
        fontWeight === 'bold' ||
        fontWeight === '700',
      italics:
        parentStyle.italics ||
        el.tagName === 'I' ||
        el.tagName === 'EM' ||
        fontStyle === 'italic',
      underline:
        parentStyle.underline ||
        el.tagName === 'U' ||
        (style.textDecoration && style.textDecoration.includes('underline')),
      color: style.color ? style.color.replace(/^#/, '') : parentStyle.color,
      alignment: textAlign || parentStyle.alignment,
      emphasisMark: this.mapTextEmphasisToDocx(
        style.textEmphasis,
        style.textEmphasisPosition
      ),
    };
  }

  private parseStyle(el: Element): {
    color?: string;
    width?: number;
    height?: number;
    textDecoration?: string;
    fontWeight?: string;
    fontStyle?: string;
    textAlign?: string;
    verticalAlign?: string;
    display?: string;
    margin?: string;
    alignItems?: string;
    justifyContent?: string;
    whiteSpace?: string;
    textEmphasis?: string;
    textEmphasisPosition?: string;
  } {
    const style = el.getAttribute('style') || '';
    const result: {
      color?: string;
      width?: number;
      height?: number;
      textDecoration?: string;
      fontWeight?: string;
      fontStyle?: string;
      textAlign?: string;
      verticalAlign?: string;
      display?: string;
      margin?: string;
      alignItems?: string;
      justifyContent?: string;
      whiteSpace?: string;
      textEmphasis?: string;
      textEmphasisPosition?: string;
    } = {};

    // 提取颜色
    const colorMatch = style.match(/color:\s*([^;]+)/);
    if (colorMatch) {
      result.color = this.rgbToHex(colorMatch[1].trim())?.toUpperCase();
    }

    // 提取文本装饰（下划线等）
    const textDecorationMatch = style.match(/text-decoration:\s*([^;]+)/);
    if (textDecorationMatch) {
      result.textDecoration = textDecorationMatch[1].trim();
    }

    // 提取宽度
    const widthMatch = style.match(/width:\s*(\d+)px/);
    if (widthMatch) {
      result.width = parseInt(widthMatch[1], 10);
    }

    // 提取高度
    const heightMatch = style.match(/height:\s*(\d+)px/);
    if (heightMatch) {
      result.height = parseInt(heightMatch[1], 10);
    }

    // 提取 font-weight
    const fontWeightMatch = style.match(/font-weight:\s*([^;]+)/);
    if (fontWeightMatch) {
      result.fontWeight = fontWeightMatch[1].trim();
    }

    // 提取 font-style
    const fontStyleMatch = style.match(/font-style:\s*([^;]+)/);
    if (fontStyleMatch) {
      result.fontStyle = fontStyleMatch[1].trim();
    }

    // 提取 text-align
    const textAlignMatch = style.match(/text-align:\s*([^;]+)/);
    if (textAlignMatch) {
      result.textAlign = textAlignMatch[1].trim();
    }

    // 提取 vertical-align
    const verticalAlignMatch = style.match(/vertical-align:\s*([^;]+)/);
    if (verticalAlignMatch) {
      result.verticalAlign = verticalAlignMatch[1].trim();
    }

    // 提取 display
    const displayMatch = style.match(/display:\s*([^;]+)/);
    if (displayMatch) {
      result.display = displayMatch[1].trim();
    }

    // 提取 margin
    const marginMatch = style.match(/margin:\s*([^;]+)/);
    if (marginMatch) {
      result.margin = marginMatch[1].trim();
    }

    // 提取 align-items
    const alignItemsMatch = style.match(/align-items:\s*([^;]+)/);
    if (alignItemsMatch) {
      result.alignItems = alignItemsMatch[1].trim();
    }

    // 提取 justify-content
    const justifyContentMatch = style.match(/justify-content:\s*([^;]+)/);
    if (justifyContentMatch) {
      result.justifyContent = justifyContentMatch[1].trim();
    }

    // 提取 white-space
    const whiteSpaceMatch = style.match(/white-space:\s*([^;]+)/);
    if (whiteSpaceMatch) {
      result.whiteSpace = whiteSpaceMatch[1].trim();
    }

    // 提取 text-emphasis
    const textEmphasisMatch = style.match(/text-emphasis:\s*([^;]+)/);
    if (textEmphasisMatch) {
      result.textEmphasis = textEmphasisMatch[1].trim();
    }

    // 提取 text-emphasis-position
    const textEmphasisPositionMatch = style.match(
      /text-emphasis-position:\s*([^;]+)/
    );
    if (textEmphasisPositionMatch) {
      result.textEmphasisPosition = textEmphasisPositionMatch[1].trim();
    }

    return result;
  }

  private rgbToHex(rgb: string): string | undefined {
    // 如果已经是十六进制颜色，直接返回
    if (/^#[0-9A-Fa-f]{6}$/.test(rgb.trim())) {
      return rgb.trim();
    }
    if (/^[0-9A-Fa-f]{6}$/.test(rgb.trim())) {
      return rgb.trim();
    }

    // 处理 rgb(r, g, b) 格式
    const rgbMatch = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (rgbMatch) {
      const r = parseInt(rgbMatch[1], 10);
      const g = parseInt(rgbMatch[2], 10);
      const b = parseInt(rgbMatch[3], 10);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理 rgba(r, g, b, a) 格式
    const rgbaMatch = rgb.match(
      /^rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\s*\)$/
    );
    if (rgbaMatch) {
      const r = parseInt(rgbaMatch[1], 10);
      const g = parseInt(rgbaMatch[2], 10);
      const b = parseInt(rgbaMatch[3], 10);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理 hsl(h, s%, l%) 格式
    const hslMatch = rgb.match(/^hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)$/);
    if (hslMatch) {
      const h = parseInt(hslMatch[1], 10);
      const s = parseInt(hslMatch[2], 10);
      const l = parseInt(hslMatch[3], 10);
      // 简单的 HSL 到 RGB 转换
      const c = ((1 - Math.abs((2 * l) / 100 - 1)) * s) / 100;
      const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
      const m = l / 100 - c / 2;
      let r = 0,
        g = 0,
        b = 0;
      if (h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      r = Math.round((r + m) * 255);
      g = Math.round((g + m) * 255);
      b = Math.round((b + m) * 255);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理颜色名称
    const colorNames: { [key: string]: string } = {
      black: '#000000',
      white: '#FFFFFF',
      red: '#FF0000',
      green: '#00FF00',
      blue: '#0000FF',
      yellow: '#FFFF00',
      purple: '#800080',
      gray: '#808080',
      grey: '#808080',
    };
    if (colorNames[rgb.toLowerCase()]) {
      return colorNames[rgb.toLowerCase()];
    }

    // 兜底：返回 undefined
    return undefined;
  }

  private async parseTable(el: Element): Promise<Table> {
    const rows: TableRow[] = [];
    const trs = el.querySelectorAll('tr');
    for (const tr of trs) {
      const cells: TableCell[] = [];
      const tds = tr.querySelectorAll('td,th');
      for (const td of tds) {
        const cellParagraphs = await this.parseHtmlNodes(td.childNodes);
        const style = this.parseStyle(td);
        const width =
          style.width || parseInt(td.getAttribute('width') || '100', 10);

        // 水平对齐
        let alignment;
        if (
          style.textAlign &&
          ['left', 'center', 'right', 'justify'].includes(style.textAlign)
        ) {
          alignment = style.textAlign;
        } else if (td.hasAttribute('align')) {
          const alignAttr = td.getAttribute('align')?.toLowerCase();
          if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
            alignment = alignAttr;
          }
        }

        // 垂直对齐
        let verticalAlign: 'center' | 'top' | 'bottom' | undefined;
        if (style.verticalAlign) {
          const v = style.verticalAlign.toLowerCase();
          if (v === 'middle' || v === 'center') verticalAlign = 'center';
          else if (v === 'top') verticalAlign = 'top';
          else if (v === 'bottom') verticalAlign = 'bottom';
        } else if (td.hasAttribute('valign')) {
          const valignAttr = td.getAttribute('valign')?.toLowerCase();
          if (valignAttr === 'middle' || valignAttr === 'center')
            verticalAlign = 'center';
          else if (valignAttr === 'top') verticalAlign = 'top';
          else if (valignAttr === 'bottom') verticalAlign = 'bottom';
        }

        // 创建单元格，设置宽度和垂直对齐
        const cellProps: any = {
          children: cellParagraphs.map(p => {
            if (p instanceof Paragraph) {
              // 创建新的段落，保留原有内容并设置对齐方式
              const paragraphProps: any = {
                children: (p as any)._children || [],
                heading: (p as any).options?.heading,
                spacing: { line: 360 },
              };

              // 设置水平对齐
              if (alignment) {
                paragraphProps.alignment = alignment;
              }

              // 设置垂直对齐
              if (verticalAlign) {
                paragraphProps.verticalAlign = verticalAlign;
              }

              return new Paragraph(paragraphProps);
            }
            return p;
          }),
          width: { size: width * 20, type: 'dxa' },
        };

        // 设置垂直对齐
        if (verticalAlign) {
          cellProps.verticalAlign = verticalAlign;
        }

        // 设置水平对齐
        if (alignment) {
          cellProps.alignment = alignment;
        }

        // 设置单元格边框
        cellProps.borders = {
          top: { style: 'single', size: 1, color: '000000' },
          bottom: { style: 'single', size: 1, color: '000000' },
          left: { style: 'single', size: 1, color: '000000' },
          right: { style: 'single', size: 1, color: '000000' },
        };

        cells.push(new TableCell(cellProps));
      }
      rows.push(new TableRow({ children: cells }));
    }
    return new Table({
      rows,
      width: {
        size: 100,
        type: 'pct',
      },
    });
  }

  // 自动识别SVG尺寸，支持ex单位
  private getSvgIntrinsicSize(
    svgText: string
  ): { width: number; height: number } | undefined {
    // 1. width/height 属性
    const widthMatch = svgText.match(/width=["']?([\d.]+)(px|ex)?["']?/i);
    const heightMatch = svgText.match(/height=["']?([\d.]+)(px|ex)?["']?/i);
    let width, height;
    if (widthMatch && heightMatch) {
      width = parseFloat(widthMatch[1]);
      height = parseFloat(heightMatch[1]);
      // 单位转换
      const widthUnit = widthMatch[2];
      const heightUnit = heightMatch[2];
      if (widthUnit === 'ex') width = width * 6; // svg转px，在这里修改可以修改ex单位换算的像素值
      if (heightUnit === 'ex') height = height * 6;
      return { width, height };
    }
    // 2. viewBox
    const viewBoxMatch = svgText.match(
      /viewBox=["']?([\d.]+) ([\d.]+) ([\d.]+) ([\d.]+)["']?/i
    );
    if (viewBoxMatch) {
      return {
        width: parseFloat(viewBoxMatch[3]),
        height: parseFloat(viewBoxMatch[4]),
      };
    }
    return undefined;
  }

  // Helper to map CSS text-emphasis to docx EmphasisMark
  private mapTextEmphasisToDocx(
    textEmphasis?: string,
    textEmphasisPosition?: string
  ):
    | {
        type: 'dot' | 'circle' | 'accent' | 'comma';
        position: 'above' | 'below';
      }
    | undefined {
    if (!textEmphasis) return undefined;

    let type: 'dot' | 'circle' | 'accent' | 'comma' = 'dot'; // Default type
    let position: 'above' | 'below' = 'above'; // Default position

    // Map emphasis type
    if (textEmphasis.includes('circle')) type = 'circle';
    else if (textEmphasis.includes('accent')) type = 'accent';
    else if (textEmphasis.includes('comma')) type = 'comma';
    // 'dot' is default

    // Map emphasis position (simplified: only supports 'under' for below)
    if (textEmphasisPosition && textEmphasisPosition.includes('under')) {
      position = 'below';
    }
    // 'above' is default

    return { type, position };
  }
}

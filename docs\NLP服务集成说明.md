# NLP服务集成说明

## 概述

本文档描述了Word试题导入工具中NLP智能分类功能的集成方案。NLP服务主要提供以下功能：

1. **题型自动判断**：通过语义分析判断题目类型，与系统默认提供的题型进行匹配
2. **知识点提取**：从题目内容中提取关键知识点（如"三角函数"、"牛顿定律"等）
3. **相似题检测**：检测数据库中与当前题目相似的题目，避免重复导入

## 架构设计

### 核心组件

- **NLPHelperService**：NLP服务的核心类，提供题型判断、知识点提取和相似题检测功能
- **配置模块**：在`config.nlp.ts`中定义NLP服务的配置参数
- **错误处理机制**：包含速率限制处理和请求重试逻辑

### 数据流

1. Word文档解析后生成题目列表
2. NLPHelperService处理题目内容
3. 返回增强后的题目信息（包含题型、知识点等）
4. 存储到数据库

## 使用方法

### 基本用法

```typescript
@Inject()
private nlpHelper: NLPHelperService;

// 初始化服务
await this.nlpHelper.init();

// 题型判断
const typeResult = await this.nlpHelper.detectQuestionType(questionText);

// 知识点提取
const pointsResult = await this.nlpHelper.extractKnowledgePoints(questionText);

// 相似题检测
const similarityResult = await this.nlpHelper.detectSimilarQuestions(question, 0.8);
```

### 批量处理

```typescript
// 批量处理题目（题型判断+知识点提取）
const nlpResult = await this.nlpHelper.batchProcessQuestions(questions);
```

## 配置说明

在`config.nlp.ts`文件中可以配置以下参数：

```typescript
{
  // API配置
  apiEndpoint: 'https://nlp-api.example.com',
  apiKey: 'your-api-key-here',
  
  // 速率限制配置
  rateLimit: {
    maxRequests: 100, // 最大请求数
    perInterval: 60000, // 时间间隔(毫秒)
  },
  
  // 重试配置
  retry: {
    attempts: 3, // 重试次数
    factor: 2, // 重试间隔因子
    minTimeout: 1000, // 最小超时时间(毫秒)
    maxTimeout: 10000, // 最大超时时间(毫秒)
  },
  
  // 默认题型和知识点列表
  defaultQuestionTypes: ['单选题', '多选题', ...],
  defaultKnowledgePoints: [{id: 1, name: '三角函数', ...}]
}
```

## 依赖包

本服务依赖以下NPM包：

- `axios`: 用于发送HTTP请求
- `async-retry`: 实现请求重试机制

请确保在项目的`package.json`中添加这些依赖。

## 注意事项

1. 在生产环境中，请替换配置文件中的API端点和密钥为实际值
2. 根据实际使用的NLP服务提供商调整API请求格式
3. 根据实际需求调整速率限制和重试配置
4. 定期更新知识点列表，以提高匹配准确率
# 图片处理工具 (image)

## 模块简介

`image` 模块专门用于处理Word文档中的图片，负责提取图片数据、处理图片关系以及将图片转换为HTML格式。

## 主要功能

- 从Word文档中提取图片
- 处理图片的位置和大小信息
- 支持多种图片格式（PNG、JPEG、GIF等）
- 处理图片的对齐方式和浮动设置

## 文件结构

```
image/
├── docx-image.ts     # Word文档图片处理
└── README.md         # 本文档
```

## API说明

### 主要函数

#### `processDrawing(drawing: Element, document: Document, imageData: Map<string, string>): HTMLImageElement | null`

处理Word文档中的图片，返回HTML图片元素。

**参数:**
- `drawing`: 图片元素
- `document`: HTML文档
- `imageData`: 图片数据映射表

**返回值:**
- 返回HTML图片元素，如果处理失败则返回null

## 图片处理流程

1. **提取图片ID**: 从`a:blip`元素中获取图片的嵌入ID
2. **获取图片数据**: 使用嵌入ID从图片数据映射表中获取图片数据
3. **设置图片尺寸**: 从`wp:extent`元素中获取图片的宽度和高度
4. **处理图片对齐方式**: 根据`wp:docPr`和`wp:positionH`等元素设置图片的对齐方式
5. **处理图片浮动设置**: 根据`wp:anchor`和`wp:inline`元素设置图片的浮动属性

## 支持的图片属性

- **尺寸**: 宽度和高度
- **对齐方式**: 左对齐、右对齐、居中对齐
- **浮动设置**: 行内、环绕文字
- **位置**: 相对于页面、页边距、栏、字符等的位置
- **边距**: 图片周围的空白区域

## 使用示例

```typescript
import { processDrawing } from '../utils/word-import/image/docx-image';
import { DOMParser } from 'xmldom';

// 解析XML
const parser = new DOMParser();
const xml = `<w:drawing xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <!-- 图片XML内容 -->
</w:drawing>`;
const doc = parser.parseFromString(xml, 'text/xml');
const drawing = doc.getElementsByTagName('w:drawing')[0];

// 创建HTML文档
const jsdom = require('jsdom');
const { JSDOM } = jsdom;
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
const document = dom.window.document;

// 准备图片数据
const imageData = new Map<string, string>();
imageData.set('rId1', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...');

// 处理图片
const imgElement = processDrawing(drawing, document, imageData);

// 将图片添加到文档
if (imgElement) {
  document.body.appendChild(imgElement);
  console.log(document.body.innerHTML);
}
```

## 支持的图片格式

- PNG (.png)
- JPEG (.jpg, .jpeg)
- GIF (.gif)
- BMP (.bmp)
- EMF (.emf) - 部分支持
- WMF (.wmf) - 部分支持

## 注意事项

1. EMF和WMF格式的图片可能无法直接显示，会使用占位符替代
2. 对于复杂的图片布局，可能需要进一步优化处理逻辑
3. 图片数据以Base64格式存储，可能会增加HTML文件的大小

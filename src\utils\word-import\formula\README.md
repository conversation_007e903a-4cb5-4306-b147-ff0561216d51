# 数学公式处理工具 (formula)

## 模块简介

`formula` 模块专门用于处理Word文档中的数学公式，将Office Math Markup Language (OMML) 格式的公式转换为LaTeX格式，并提供公式渲染功能。

## 主要功能

- 使用mammoth库解析OMML格式的数学公式
- 将OMML转换为LaTeX格式
- 使用在线服务（CodeCogs和Google Chart API）渲染公式
- 支持复杂的数学表达式（分数、根号、上下标等）
- 自动降级处理，确保公式始终能够显示
- 根据公式复杂度动态调整渲染参数

## 文件结构

```
formula/
├── docx-formula.ts     # 负责将LaTeX公式渲染为HTML元素（创建图片元素、设置样式等）
├── index.ts            # 模块主入口，提供公式处理的统一接口
├── latex-renderer.ts   # 负责将LaTeX公式转换为图片URL（使用在线服务）
├── mammoth-formula.ts  # 负责将OMML格式的公式转换为LaTeX格式（使用mammoth库）
└── README.md           # 本文档
```

## API说明

### 主要函数

#### `processMath(oMath: Element, document: Document, useScreenshotMethod: boolean = false): Promise<HTMLElement>`

处理Word文档中的公式，返回HTML元素。

**参数:**
- `oMath`: 公式元素
- `document`: HTML文档
- `useScreenshotMethod`: 是否使用在线服务渲染公式（默认为false）

**返回值:**
- 返回Promise，解析为包含公式的HTML元素

#### `processFormula(oMath: Element, useScreenshotMethod: boolean = false): Promise<{ latex: string; imageUrl: string; isScreenshot?: boolean }>`

处理公式，返回LaTeX和图片URL。

**参数:**
- `oMath`: 公式元素
- `useScreenshotMethod`: 是否使用在线服务渲染公式（默认为false）

**返回值:**
- 返回Promise，解析为包含LaTeX和图片URL的对象

#### `parseOMMLToLaTeX(oMath: Element): Promise<string>`

将OMML格式的公式转换为LaTeX格式。

**参数:**
- `oMath`: 公式元素

**返回值:**
- 返回Promise，解析为LaTeX格式的公式字符串

#### `convertFormulaToImage(formula: string, dpi: number = 200): string`

将LaTeX公式转换为图片。

**参数:**
- `formula`: LaTeX格式的公式
- `dpi`: 图片分辨率（默认为200）

**返回值:**
- 返回图片的Data URL

### 辅助函数

#### `getFormulaComplexity(latex: string): number`

计算公式的复杂度。

**参数:**
- `latex`: LaTeX格式的公式

**返回值:**
- 返回公式复杂度的数值

#### `replaceCommonSymbols(text: string): string`

替换常见的数学符号。

**参数:**
- `text`: 包含数学符号的文本

**返回值:**
- 返回替换后的文本

## 使用示例

```typescript
import { processMath } from '../utils/word-import/formula/docx-formula';
import { DOMParser } from 'xmldom';

// 解析XML
const parser = new DOMParser();
const xml = `<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math">
  <!-- 公式XML内容 -->
</m:oMath>`;
const doc = parser.parseFromString(xml, 'text/xml');
const oMath = doc.getElementsByTagName('m:oMath')[0];

// 创建HTML文档
const jsdom = require('jsdom');
const { JSDOM } = jsdom;
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
const document = dom.window.document;

// 处理公式（异步）
async function renderFormula() {
  // 处理公式
  const mathElement = await processMath(oMath, document);

  // 将公式添加到文档
  document.body.appendChild(mathElement);

  // 输出HTML
  console.log(document.body.innerHTML);
}

renderFormula();
```

## 渲染流程

1. 从Word文档中提取OMML格式的公式
2. 使用mammoth库将OMML转换为LaTeX格式（mammoth-formula.ts）
3. 使用在线服务将LaTeX公式转换为图片URL（latex-renderer.ts）
4. 创建HTML元素，包含公式图片（docx-formula.ts）
5. 如果渲染失败，使用备用方法显示公式文本

## 支持的公式类型

- 分数 (fractions)
- 根号 (radicals)
- 上标和下标 (superscripts and subscripts)
- 矩阵 (matrices)
- 积分 (integrals)
- 求和 (summations)
- 极限 (limits)
- 各种数学符号 (mathematical symbols)

## 注意事项

1. 公式渲染支持多种方式：
   - 在线服务（CodeCogs和Google Chart API）
   - 自动降级处理，确保公式始终能够显示
2. 渲染方式选择建议：
   - 对于复杂公式，建议使用在线服务渲染
   - 对于特殊公式，已添加专门处理
3. 性能优化：
   - 已实现公式复杂度分析，动态调整渲染参数
   - 已实现多级降级处理，确保公式始终能够显示
4. 未来优化方向：
   - 集成更强大的本地渲染库，如MathJax或KaTeX
   - 实现公式图片缓存机制，减少网络请求
   - 提供更多自定义选项
5. 特殊公式处理：
   - 已添加对二次方程公式的专门处理
   - 已添加对根号结构的专门处理
   - 已添加对分数结构的专门处理

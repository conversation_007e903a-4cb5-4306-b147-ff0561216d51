# 📁 测试文件目录说明

本目录包含了Word试题解析服务的测试文件，用于验证导入和导出功能。

## 📋 文件结构

```
testFiles/
├── README.md                    ← 📚 本说明文件
├── input-word-sample.docx       ← 📄 Word导入测试文件
├── input-html-sample.html       ← 🌐 HTML导出测试文件
├── html-template.html           ← 📝 HTML模板文件
└── output-word-result.docx      ← 📤 Word导出结果文件
```

## 📝 文件说明

### 🔤 输入文件（测试原始文件）

#### 📄 `input-word-sample.docx`
- **用途**: Word文档解析功能的测试文件
- **内容**: 包含各种题型的Word试题文档
- **测试功能**: 
  - Word文档解析
  - 题型识别
  - 内容提取
  - 格式保持

#### 🌐 `input-html-sample.html`
- **用途**: HTML转Word导出功能的测试文件
- **内容**: 包含丰富格式的HTML内容
- **测试功能**:
  - HTML解析
  - 样式转换
  - Word文档生成
  - 格式保持

### 🛠️ 辅助文件

#### 📝 `html-template.html`
- **用途**: HTML导出过程的模板文件
- **作用**: 提供完整的HTML结构框架
- **功能**: 
  - 生成完整的HTML文档
  - 便于浏览器预览
  - 样式统一管理

### 📤 输出文件（测试结果文件）

#### 📄 `output-word-result.docx`
- **用途**: HTML转Word导出功能的测试结果
- **生成方式**: 由 `input-html-sample.html` 转换生成
- **验证内容**:
  - 文档结构完整性
  - 格式转换准确性
  - 样式保持效果

## 🚀 使用方法

### Word解析测试

```bash
# 解析Word文档
node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-sample.docx

# 预期输出
# - 解析结果JSON文件
# - 控制台输出解析统计
```

### HTML转Word测试

```bash
# HTML转Word导出
node --require ts-node/register src/example/word-export-example.ts

# 预期输出
# - testFiles/output-word-result.docx
# - 控制台输出转换统计
```

### API接口测试

```bash
# 启动服务器
npm run dev

# 测试Word解析API
curl -X POST http://localhost:3131/word/parse \
  -H "Content-Type: application/json" \
  -d '{"filePath": "testFiles/input-word-sample.docx"}'

# 测试文件上传API
curl -X POST http://localhost:3131/word/upload \
  -F "file=@testFiles/input-word-sample.docx"
```

## 🔄 文件更新说明

### 自动生成的文件

以下文件会在测试过程中自动生成或更新：

- `output-word-result.docx` - 每次运行HTML转Word测试时更新
- `input-word-sample_parsed.json` - Word解析结果（如果启用JSON输出）

### 手动维护的文件

以下文件需要手动维护和更新：

- `input-word-sample.docx` - 根据测试需要更新Word内容
- `input-html-sample.html` - 根据测试需要更新HTML内容
- `html-template.html` - 根据模板需要调整结构

## 📊 测试验证

### 成功标准

#### Word解析测试
- ✅ 能够正确识别题型
- ✅ 能够提取题干和选项
- ✅ 能够保持原始格式
- ✅ 能够输出结构化数据

#### HTML转Word测试
- ✅ 生成的Word文档能正常打开
- ✅ 文本内容完整转换
- ✅ 基本格式得到保持
- ✅ 文件大小合理（通常5-50KB）

### 常见问题

#### 文件不存在错误
```bash
# 确保在项目根目录运行命令
pwd  # 应该显示项目根目录
ls testFiles/  # 应该能看到测试文件
```

#### 权限错误
```bash
# 检查文件权限
ls -la testFiles/
# 如果需要，修改权限
chmod 644 testFiles/*
```

## 🎯 扩展测试

### 添加新的测试文件

1. **添加Word测试文件**:
   ```bash
   cp your-test.docx testFiles/input-word-custom.docx
   ```

2. **添加HTML测试文件**:
   ```bash
   cp your-test.html testFiles/input-html-custom.html
   ```

3. **运行自定义测试**:
   ```bash
   node --require ts-node/register src/example/word-parser-example.ts testFiles/input-word-custom.docx
   ```

### 批量测试

```bash
# 测试所有Word文件
for file in testFiles/input-word-*.docx; do
  echo "测试文件: $file"
  node --require ts-node/register src/example/word-parser-example.ts "$file"
done
```

---

**💡 提示**: 建议在修改测试文件前先备份原始文件，以便随时恢复到已知的工作状态。

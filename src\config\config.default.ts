import { MidwayConfig } from '@midwayjs/core';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1745548895615_405',
  koa: {
    port: 3131,
  },

  busboy: {
    mode: 'file',
    whitelist: ['.docx'],
    // 仅允许下面这些文件类型可以上传
    mimeTypeWhiteList: {
      '.docx':
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.doc': 'application/msword',
    },
    limits: {
      fileSize: 1024 * 1024 * 20,
    },
  },
  bullmq: {
    defaultConnection: {
      host: '*************',
      port: 6379,
      password: 'Clouddeep&&Redis9910',
    },
    // 可选，队列前缀
    defaultPrefix: '{midway-bullmq}',
  },

  // Redis配置
  redis: {
    client: {
      port: 6379,
      host: '*************',
      password: 'Clouddeep&&Redis9910',
      db: 0,
    },
  },
} as MidwayConfig;

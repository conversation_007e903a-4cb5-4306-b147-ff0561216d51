# Word文档解析工具 (docx)

## 模块简介

`docx` 模块是Word文档解析器的核心组件，负责将Word文档（.docx格式）解析为HTML格式，同时保留文档中的颜色、格式、图片、表格和数学公式等信息。

## 主要功能

- 解压并解析.docx文件
- 提取文档结构和内容
- 提取样式信息（颜色、字体、大小等）
- 处理图片关系和提取图片数据
- 将Word XML转换为HTML

## 文件结构

```
docx/
├── index.ts           # 模块入口，提供统一的API
├── parser.ts          # 基础解析功能，提取XML和样式
├── html-converter.ts  # 将XML转换为HTML
└── README.md          # 本文档
```

## API说明

### 主要函数

#### `parseDocxWithColors(filePath: string, useScreenshotMethod: boolean = false): Promise<string>`

解析Word文档，提取颜色信息，返回HTML内容。

**参数:**
- `filePath`: Word文档路径
- `useScreenshotMethod`: 是否使用截图方式处理公式（默认为false）

**返回值:**
- 返回解析后的HTML字符串

**示例:**
```typescript
import { parseDocxWithColors } from '../utils/docx';

// 解析Word文档
const html = await parseDocxWithColors('path/to/document.docx');
console.log(html); // 输出HTML内容
```

### 辅助函数

#### `extractDocxXml(filePath: string): Promise<{documentDoc: Document; stylesDoc: Document; relsDoc: Document | null; zip: JSZip}>`

从Word文档中提取XML内容。

#### `extractStyles(stylesDoc: Document): Map<string, any>`

从styles.xml中提取样式信息。

#### `extractImageRelations(relsDoc: Document): Map<string, string>`

从关系文件中提取图片关系。

#### `extractImageData(zip: JSZip, imageRelations: Map<string, string>): Promise<Map<string, string>>`

从ZIP中提取图片数据。

#### `convertToHtml(documentDoc: Document, styles: Map<string, any>, imageData: Map<string, string>, useScreenshotMethod: boolean): string`

将Word XML转换为HTML。

#### `addFormulaStyles(html: string): string`

添加公式样式到HTML。

## 使用示例

```typescript
import { parseDocxWithColors } from '../utils/docx';
import * as fs from 'fs';

async function parseDocument() {
  try {
    // 解析Word文档
    const html = await parseDocxWithColors('examples/document.docx');
    
    // 将结果保存到文件
    fs.writeFileSync('examples/output.html', html);
    
    console.log('文档解析成功，结果已保存到output.html');
  } catch (error) {
    console.error('解析文档失败:', error);
  }
}

parseDocument();
```

## 依赖项

- JSZip: 用于解压.docx文件
- xmldom: 用于解析XML
- JSDOM: 用于操作DOM

## 注意事项

1. 仅支持.docx格式文件，不支持.doc格式
2. 对于复杂的文档结构，可能需要进一步优化解析逻辑
3. 数学公式处理支持两种模式：LaTeX渲染和截图方式

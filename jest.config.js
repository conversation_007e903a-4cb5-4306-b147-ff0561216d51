module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testPathIgnorePatterns: ['<rootDir>/test/fixtures'],
  coveragePathIgnorePatterns: ['<rootDir>/test/'],

  // 测试超时设置
  testTimeout: 30000, // 30秒

  // 并发设置，避免端口冲突
  maxWorkers: 1, // 串行执行测试，避免端口冲突

  // 详细输出
  verbose: true,

  // 清理模拟
  clearMocks: true,
  restoreMocks: true,

  // 强制退出，避免进程无法正常退出
  forceExit: true,

  // 检测打开的句柄
  detectOpenHandles: true,
};
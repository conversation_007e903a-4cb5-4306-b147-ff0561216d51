import { Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { DataListener } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 文件清理监听器
 * 用于定期清理过期的临时文件
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class FileCleanupListener extends DataListener<void> {
  private readonly FILE_LIFETIME = 60 * 60 * 1000; // 1小时
  private readonly OUTPUT_DIR = path.join(
    process.cwd(),
    'temp',
    'word-exports'
  );
  private intervalHandler: NodeJS.Timeout;

  /**
   * 初始化数据
   */
  async initData() {
    // 确保输出目录存在
    if (!fs.existsSync(this.OUTPUT_DIR)) {
      fs.mkdirSync(this.OUTPUT_DIR, { recursive: true });
    }
    // 初始执行一次清理
    await this.cleanupFiles();
  }

  /**
   * 数据订阅更新
   */
  onData(setData) {
    // 每小时执行一次清理
    this.intervalHandler = setInterval(async () => {
      await this.cleanupFiles();
      setData(); // 更新数据状态
    }, this.FILE_LIFETIME);
  }

  /**
   * 清理过期文件
   */
  private async cleanupFiles() {
    try {
      const files = fs.readdirSync(this.OUTPUT_DIR);
      const now = Date.now();

      for (const file of files) {
        const filePath = path.join(this.OUTPUT_DIR, file);
        const stats = fs.statSync(filePath);
        const fileAge = now - stats.mtimeMs;

        if (fileAge > this.FILE_LIFETIME) {
          fs.unlinkSync(filePath);
          console.log(`删除过期文件: ${filePath}`);
        }
      }
    } catch (error) {
      console.error('清理过期文件失败:', error);
    }
  }

  /**
   * 清理资源
   */
  async destroyListener() {
    if (this.intervalHandler) {
      clearInterval(this.intervalHandler);
    }
  }
}

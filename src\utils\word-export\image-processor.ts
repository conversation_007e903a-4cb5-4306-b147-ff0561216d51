import * as sharp from 'sharp';

export class ImageProcessor {
  /**
   * 将SVG转换为PNG Buffer
   */
  static async svgBase64ToPngBuffer(
    svgData: string,
    width: number,
    height: number
  ): Promise<Buffer> {
    let svgBuffer: Buffer;
    if (/;base64,/.test(svgData)) {
      const base64 = svgData.split(';base64,').pop()!;
      svgBuffer = Buffer.from(base64, 'base64');
    } else {
      const svgText = decodeURIComponent(svgData.split(',').pop()!);
      svgBuffer = Buffer.from(svgText);
    }
    // 输出更高分辨率，白色背景去黑边
    return await sharp(svgBuffer)
      .resize(Math.round(width * 3), Math.round(height * 3), {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 },
      })
      .png()
      .toBuffer();
  }

  /**
   * 自动识别SVG尺寸
   */
  static getSvgIntrinsicSize(
    svgText: string
  ): { width: number; height: number } | undefined {
    // 1. width/height 属性
    const widthMatch = svgText.match(/width=["']?([\d.]+)(px|ex)?["']?/i);
    const heightMatch = svgText.match(/height=["']?([\d.]+)(px|ex)?["']?/i);
    let width, height;
    if (widthMatch && heightMatch) {
      width = parseFloat(widthMatch[1]);
      height = parseFloat(heightMatch[1]);
      // 单位转换
      const widthUnit = widthMatch[2];
      const heightUnit = heightMatch[2];
      if (widthUnit === 'ex') width = width * 6; // svg转px，在这里修改可以修改ex单位换算的像素值
      if (heightUnit === 'ex') height = height * 6;
      return { width, height };
    }
    // 2. viewBox
    const viewBoxMatch = svgText.match(
      /viewBox=["']?([\d.]+) ([\d.]+) ([\d.]+) ([\d.]+)["']?/i
    );
    if (viewBoxMatch) {
      return {
        width: parseFloat(viewBoxMatch[3]),
        height: parseFloat(viewBoxMatch[4]),
      };
    }
    return undefined;
  }
}

import { MidwayConfig } from '@midwayjs/core';

/**
 * NLP服务配置
 */
export default {
  nlp: {
    // API配置
    apiEndpoint: 'https://nlp-api.example.com', // 替换为实际的API端点
    apiKey: 'your-api-key-here', // 替换为实际的API密钥

    // 速率限制配置
    rateLimit: {
      maxRequests: 100, // 最大请求数
      perInterval: 60000, // 时间间隔(毫秒)，这里是1分钟
    },

    // 重试配置
    retry: {
      attempts: 3, // 重试次数
      factor: 2, // 重试间隔因子
      minTimeout: 1000, // 最小超时时间(毫秒)
      maxTimeout: 10000, // 最大超时时间(毫秒)
    },

    // 默认题型列表
    defaultQuestionTypes: [
      '单选题',
      '多选题',
      '判断题',
      '填空题',
      '简答题',
      '计算题',
      '综合题',
      '分析题',
      '论述题',
      '应用题',
    ],

    // 默认知识点列表（示例）
    defaultKnowledgePoints: [
      {
        id: 1,
        name: '三角函数',
        category: '数学',
        weight: 1.0,
      },
      {
        id: 2,
        name: '牛顿定律',
        category: '物理',
        weight: 1.0,
      },
      {
        id: 3,
        name: '化学方程式',
        category: '化学',
        weight: 1.0,
      },
      {
        id: 4,
        name: '细胞结构',
        category: '生物',
        weight: 1.0,
      },
      {
        id: 5,
        name: '古代文学',
        category: '语文',
        weight: 1.0,
      },
      {
        id: 6,
        name: '英语语法',
        category: '英语',
        weight: 1.0,
      },
    ],
  },
} as MidwayConfig;

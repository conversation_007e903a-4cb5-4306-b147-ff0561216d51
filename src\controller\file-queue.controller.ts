import { Controller, Inject, Post, Get, Param, Body } from '@midwayjs/core';
import { FileQueueService, FileParseTask } from '../service/file-queue.service';

/**
 * 文件队列控制器
 */
@Controller('/api/file-queue')
export class FileQueueController {
  @Inject()
  fileQueueService: FileQueueService;

  /**
   * 添加文件解析任务
   * @param task 文件解析任务
   * @returns 任务ID
   */
  @Post('/task')
  async addTask(@Body() task: FileParseTask) {
    const taskId = await this.fileQueueService.addTask(task);
    return {
      success: true,
      taskId,
      message: '任务已添加到队列',
    };
  }

  /**
   * 获取任务进度
   * @param taskId 任务ID
   * @returns 任务进度信息
   */
  @Get('/task/:taskId')
  async getTaskProgress(@Param('taskId') taskId: string) {
    const progress = await this.fileQueueService.getTaskProgress(taskId);
    if (!progress) {
      return {
        success: false,
        message: '任务不存在',
      };
    }
    return {
      success: true,
      data: progress,
    };
  }

  /**
   * 获取队列统计信息
   * @returns 队列统计信息
   */
  @Get('/stats')
  async getQueueStats() {
    const stats = await this.fileQueueService.getQueueStats();
    return {
      success: true,
      data: stats,
    };
  }
}

import { Provide } from '@midwayjs/core';
import * as mammoth from 'mammoth';
import * as fs from 'fs';
import * as path from 'path';
import { JSDOM } from 'jsdom';
import { parseDocxWithColors } from '../utils/docx';

/**
 * 试题类型枚举
 */
export enum QuestionType {
  SINGLE_CHOICE = '单选题',
  MULTIPLE_CHOICE = '多选题',
  TRUE_FALSE = '判断题',
  FILL_BLANK = '填空题',
  SHORT_ANSWER = '简答题',
  CALCULATION = '计算题',
  UNKNOWN = '未知题型',
}

/**
 * 选项结构接口
 */
export interface QuestionOption {
  [key: string]: string;
}

/**
 * 试题结构接口
 */
export interface Question {
  id?: string; // 题目ID
  type: QuestionType | string; // 题型
  content: string; // 题干内容
  options?: QuestionOption[] | string[]; // 选项（选择题），支持新旧两种格式
  answer?: string; // 答案
  explanation?: string; // 解析
  images?: string[]; // 图片（Base64格式）
  tables?: Table[]; // 表格
  raw?: string; // 原始内容
  knowledgePoints?: string[]; // 知识点
  difficulty?: string; // 难度
  level?: string; // 题目分层
  pageNumber?: number; // 页码
  answerArea?: number; // 答题区
  [key: string]: any; // 额外字段
}

/**
 * 表格结构接口
 */
export interface Table {
  headers: string[];
  rows: string[][];
}

/**
 * Word解析结果接口
 */
export interface ParseResult {
  questions: Question[];
  images: { [key: string]: string }; // 图片映射表 {id: base64}
  tables: Table[];
  raw: string; // 原始HTML
  success: boolean;
  message?: string;
  warnings?: any[];
}

// @Autoload()
@Provide()
export class WordParserService {
  // @Init()
  // async init() {
  //   try {
  //     const result = await this.parseWordFile(
  //       'E:\\git\\github\\homework-design\\question-import-word\\examples\\题库模板.docx'
  //       // 'E:\\codes\\homework-design\\question-import-word\\examples\\题库模板.docx'
  //     );
  //     // result的raw的值替换进test.html的body中
  //     const html = fs.readFileSync(
  //       'E:\\git\\github\\homework-design\\question-import-word\\examples\\template.html',
  //       // 'E:\\codes\\homework-design\\question-import-word\\examples\\template.html',
  //       'utf8'
  //     );
  //     const newHtml = html.replace('{{raw}}', result.raw);
  //     fs.writeFileSync(
  //       'E:\\git\\github\\homework-design\\question-import-word\\examples\\test.html',
  //       // 'E:\\codes\\homework-design\\question-import-word\\examples\\test.html',
  //       newHtml
  //     );
  //     console.log('测试文件生成成功: test.html');
  //   } catch (error) {
  //     console.error('初始化失败:', error);
  //   }
  // }

  /**
   * 解析Word文档
   * @param filePath Word文档路径
   * @returns 解析结果
   */
  async parseWordFile(filePath: string): Promise<ParseResult> {
    console.log('开始解析Word文件:', filePath);
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return {
          questions: [],
          images: {},
          tables: [],
          raw: '',
          success: false,
          message: '文件不存在',
        };
      }

      // 检查文件扩展名
      const ext = path.extname(filePath).toLowerCase();
      if (ext !== '.docx') {
        return {
          questions: [],
          images: {},
          tables: [],
          raw: '',
          success: false,
          message: '仅支持.docx格式文件',
        };
      }

      // 尝试使用新的解析服务，保留颜色信息
      let html: string;
      try {
        console.log('使用DocxParser解析Word文档...');
        // 使用新的解析服务
        html = await parseDocxWithColors(filePath);
        console.log('DocxParser解析成功');
      } catch (error) {
        console.warn('DocxParser解析失败，回退到mammoth:', error);

        // 如果新的解析服务失败，回退到mammoth
        const result = await mammoth.convertToHtml(
          { path: filePath },
          {
            styleMap: [
              // 段落对齐方式映射
              'p[style-name="center"] => p[style="text-align:center"]',
              'p[style-name="right"] => p[style="text-align:right"]',
              'p[style-name="left"] => p[style="text-align:left"]',

              // 通用颜色处理 - 直接使用内联样式
              'r[color="#FF0000"] => span[style="color:#FF0000"]',
              'r[color="#0000FF"] => span[style="color:#0000FF"]',
              'r[color="#00FF00"] => span[style="color:#00FF00"]',
              'r[color="#FF00FF"] => span[style="color:#FF00FF"]',
              'r[color="#00FFFF"] => span[style="color:#00FFFF"]',
              'r[color="#FFFF00"] => span[style="color:#FFFF00"]',
              'r[color="#000000"] => span[style="color:#000000"]',
              'r[color="#808080"] => span[style="color:#808080"]',
              'r[color="#800000"] => span[style="color:#800000"]',
              'r[color="#808000"] => span[style="color:#808000"]',
              'r[color="#008000"] => span[style="color:#008000"]',
              'r[color="#800080"] => span[style="color:#800080"]',
              'r[color="#008080"] => span[style="color:#008080"]',
              'r[color="#C00000"] => span[style="color:#C00000"]',
              'r[color="#C0C000"] => span[style="color:#C0C000"]',
              'r[color="#00C000"] => span[style="color:#00C000"]',
              'r[color="#C000C0"] => span[style="color:#C000C0"]',
              'r[color="#00C0C0"] => span[style="color:#00C0C0"]',

              // 通用颜色处理 - 处理任意颜色
              'r[color] => span[style="color:{color}"]',

              // 字体粗细映射
              'b => strong',
              'i => em',
              'u => span[style="text-decoration:underline"]',
            ],
            ignoreEmptyParagraphs: true,
          }
        );

        html = result.value;

        // 如果有警告，记录下来
        if (result.messages && result.messages.length > 0) {
          console.warn('Mammoth转换警告:', result.messages);
        }
      }

      // 提取试题内容
      const parseResult = this.parseHtmlContent(html);
      console.log('试题解析完成');
      return {
        ...parseResult,
        raw: html,
        success: true,
        message: '解析成功',
      };
    } catch (error) {
      console.error('解析Word文件失败:', error);
      return {
        questions: [],
        images: {},
        tables: [],
        raw: '',
        success: false,
        message: `解析失败: ${error.message}`,
      };
    }
  }

  /**
   * 解析HTML内容，提取试题
   * @param html HTML内容
   * @returns 解析结果
   */
  private parseHtmlContent(
    html: string
  ): Omit<ParseResult, 'raw' | 'success' | 'message'> {
    // 创建临时DOM解析HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // 提取图片
    const images: { [key: string]: string } = {};
    const imgElements = document.querySelectorAll('img');
    imgElements.forEach((img: Element, index: number) => {
      const src = img.getAttribute('src');
      if (src && src.startsWith('data:')) {
        const id = `img_${index}`;
        images[id] = src;
        // 不修改原始图片，只记录映射关系
      }
    });

    // 提取表格
    const tables: Table[] = [];
    const tableElements = document.querySelectorAll('table');
    tableElements.forEach((table: Element) => {
      const tableData: Table = { headers: [], rows: [] };

      // 处理表头（第一行）
      const headerRow = table.querySelector('tr');
      if (headerRow) {
        const headerCells = headerRow.querySelectorAll('th, td');
        headerCells.forEach((cell: Element) => {
          tableData.headers.push(cell.textContent?.trim() || '');
        });
      }

      // 处理数据行（从第二行开始）
      const rows = table.querySelectorAll('tr');
      for (let i = 1; i < rows.length; i++) {
        const rowData: string[] = [];
        const cells = rows[i].querySelectorAll('td');
        cells.forEach((cell: Element) => {
          rowData.push(cell.textContent?.trim() || '');
        });
        if (rowData.length > 0) {
          tableData.rows.push(rowData);
        }
      }

      tables.push(tableData);
      // 不修改原始表格，只记录映射关系
    });

    // 提取段落文本 - 保留原始HTML，包括所有样式
    const paragraphs = document.querySelectorAll('p');
    const textContents = Array.from(paragraphs).map((p: any) => p.outerHTML);

    // 预处理：检测题型、题干、选项等标记
    const processedContents = this.preprocessHtmlContent(textContents);

    // 解析试题
    const questions = this.parseQuestions(processedContents, images, tables);

    return {
      questions,
      images,
      tables,
    };
  }

  /**
   * 预处理HTML内容，标记题型、题干、选项等
   * @param paragraphs 原始段落内容
   * @returns 处理后的段落内容
   */
  private preprocessHtmlContent(paragraphs: string[]): string[] {
    const processed = [...paragraphs];
    const optionRegex = /^\s*([A-Z])[.、：\s]/;

    // 查找题型和题干的位置
    // let currentQuestionStart = -1;
    let currentContentStart = -1;

    for (let i = 0; i < processed.length; i++) {
      const paragraph = processed[i];
      const plainText = paragraph.replace(/<[^>]*>/g, '').trim();

      // 标记题型位置
      if (paragraph.includes('【题型】')) {
        // currentQuestionStart = i;
        currentContentStart = -1; // 重置题干位置
      }

      // 标记题干位置
      if (paragraph.includes('【题干】')) {
        currentContentStart = i;
      }

      // 如果已找到题干，检查是否为选项开始
      if (currentContentStart !== -1 && optionRegex.test(plainText)) {
        const match = plainText.match(optionRegex);
        if (match && match[1] === 'A') {
          // 找到A选项，将其标记为选项开始
          // 不修改原始内容，只在解析时使用这个信息
          break;
        }
      }
    }

    return processed;
  }

  /**
   * 解析试题
   * @param paragraphs 段落内容（包含完整的HTML）
   * @param images 图片映射表
   * @param tables 表格数据
   * @returns 试题数组
   */
  private parseQuestions(
    paragraphs: string[],
    _images: { [key: string]: string },
    _tables: Table[]
  ): Question[] {
    // 注意：_images 和 _tables 参数在当前实现中未直接使用，
    // 但保留它们以便未来扩展功能，如在题目中引用图片和表格
    const questions: Question[] = [];
    let currentQuestionType = null;
    let currentContent = '';
    let currentOptions: string[] = [];
    let currentAnswer = '';
    let currentExplanation = '';
    let currentKnowledgePoints: string[] = [];
    let currentDifficulty = '';
    let currentLevel = '';
    let currentPageNumber: number | null = null;
    let currentAnswerArea: number | null = null;
    let inContent = false;
    let inOptions = false;
    let inAnswer = false;
    let inExplanation = false;

    // 选项的正则表达式 - 匹配以A-Z开头，后跟.、：或空格的文本
    // 增强选项识别的正则表达式，支持更多格式
    const optionRegex = /^\s*([A-Z])[.、．:：\s]/;

    // 处理每个段落
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      // 移除HTML标签获取纯文本，用于识别
      const plainText = paragraph.replace(/<[^>]*>/g, '').trim();

      // 检查是否包含题型标识
      if (paragraph.includes('【题型】')) {
        // 如果已经有一个题目，保存它
        if (
          currentQuestionType &&
          (currentContent || currentOptions.length > 0)
        ) {
          // 保存当前题目
          this.saveCurrentQuestion(
            questions,
            currentQuestionType,
            currentContent,
            currentOptions,
            currentAnswer,
            currentExplanation,
            currentKnowledgePoints,
            currentDifficulty,
            currentLevel,
            currentPageNumber,
            currentAnswerArea
          );
        }

        // 重置状态
        const resetState = this.resetQuestionState();
        currentContent = resetState.content;
        currentOptions = resetState.options;
        currentAnswer = resetState.answer;
        currentExplanation = resetState.explanation;
        currentKnowledgePoints = resetState.knowledgePoints;
        currentDifficulty = resetState.difficulty;
        currentLevel = resetState.level;
        currentPageNumber = resetState.pageNumber;
        currentAnswerArea = resetState.answerArea;
        inContent = resetState.inContent;
        inOptions = resetState.inOptions;
        inAnswer = resetState.inAnswer;
        inExplanation = resetState.inExplanation;

        // 提取题型
        const typeMatch = paragraph.match(/【题型】(.*?)(?=<\/|$)/);
        if (typeMatch && typeMatch[1]) {
          currentQuestionType = typeMatch[1].trim();
        } else {
          currentQuestionType = plainText.replace('【题型】', '').trim();
        }
        continue;
      }

      // 如果没有题型标记，跳过解析
      if (!currentQuestionType) continue;

      // 检查是否是题干标记
      if (paragraph.includes('【题干】')) {
        // 如果已经在处理一个题目，保存它
        if (inContent || inOptions || inAnswer || inExplanation) {
          // 保存当前题目
          this.saveCurrentQuestion(
            questions,
            currentQuestionType,
            currentContent,
            currentOptions,
            currentAnswer,
            currentExplanation,
            currentKnowledgePoints,
            currentDifficulty,
            currentLevel,
            currentPageNumber,
            currentAnswerArea
          );

          // 重置状态
          const resetState = this.resetQuestionState();
          currentContent = resetState.content;
          currentOptions = resetState.options;
          currentAnswer = resetState.answer;
          currentExplanation = resetState.explanation;
          currentKnowledgePoints = resetState.knowledgePoints;
          currentDifficulty = resetState.difficulty;
          currentLevel = resetState.level;
          currentPageNumber = resetState.pageNumber;
          currentAnswerArea = resetState.answerArea;
          inContent = resetState.inContent;
          inOptions = resetState.inOptions;
          inAnswer = resetState.inAnswer;
          inExplanation = resetState.inExplanation;
        }

        inContent = true;
        inOptions = false;
        inAnswer = false;
        inExplanation = false;

        // 提取题干内容（去除【题干】标记）
        if (plainText !== '【题干】') {
          const contentText = paragraph.replace(/【题干】/g, '').trim();
          if (contentText) {
            currentContent = contentText;
          }
        }
        continue;
      }

      // 检查是否包含选项标记（如A. B. C.等）
      // 这是一个预处理步骤，用于提前检测选项
      const optionText = plainText.replace(/<[^>]*>/g, '').trim();
      if (optionRegex.test(optionText)) {
        const optionMatch = optionText.match(optionRegex);
        if (optionMatch && optionMatch[1] === 'A') {
          // 如果是A选项，则结束题干部分，开始选项部分
          inContent = false;
          inOptions = true;
          currentOptions = [paragraph];
          continue;
        }
      }

      // 检查是否是选项
      const optionMatch = plainText.match(optionRegex);

      // 如果是选项格式（如A. B. C.等）
      if (optionMatch) {
        const optionLetter = optionMatch[1]; // 获取选项字母

        // 如果是A选项，则开始新的选项部分
        if (optionLetter === 'A') {
          // 关键修改：无论当前是否在处理题干，遇到A选项都表示题干结束，开始处理选项
          inContent = false;
          inOptions = true;
          inAnswer = false;
          inExplanation = false;
          currentOptions = []; // 重置选项数组
          currentOptions.push(paragraph); // 添加A选项
          console.log('找到A选项:', plainText);
        }
        // 如果是其他选项，且当前正在处理选项，则添加到选项数组
        else if (inOptions) {
          currentOptions.push(paragraph);
          console.log('添加选项:', optionLetter, plainText);
        }
        // 如果当前不是在处理选项，但遇到了B/C/D等选项
        // 这种情况可能是解析错误，我们将其添加到选项中
        else {
          // 如果之前没有选项，创建一个空的A选项占位
          if (currentOptions.length === 0) {
            currentOptions.push('<p>A. [选项缺失]</p>');
            console.log('添加缺失的A选项占位');
          }
          currentOptions.push(paragraph);
          inContent = false;
          inOptions = true;
          console.log('添加非A开头的选项:', optionLetter, plainText);
        }
        continue;
      }

      // 如果当前在处理题干，且不是选项，则添加到题干内容
      if (inContent) {
        currentContent += paragraph;
        continue;
      }

      // 处理答案标记
      if (paragraph.includes('【答案】')) {
        inContent = false;
        inOptions = false;
        inAnswer = true;
        inExplanation = false;
        // 提取答案内容（去除【答案】标记）
        const answerMatch = paragraph.match(/【答案】(.*?)(?=<\/|$)/);
        if (answerMatch && answerMatch[1]) {
          currentAnswer = paragraph.replace(/【答案】/g, '').trim();
        } else {
          currentAnswer = paragraph;
        }
        console.log('找到答案标记');
        continue;
      }

      // 处理解析标记
      if (paragraph.includes('【解析】')) {
        inContent = false;
        inOptions = false;
        inAnswer = false;
        inExplanation = true;
        // 提取解析内容（去除【解析】标记）
        const explanationMatch = paragraph.match(/【解析】(.*?)(?=<\/|$)/);
        if (explanationMatch && explanationMatch[1]) {
          currentExplanation = paragraph.replace(/【解析】/g, '').trim();
        } else {
          currentExplanation = paragraph;
        }
        console.log('找到解析标记');
        continue;
      }

      // 处理知识点标记
      if (paragraph.includes('【知识点】')) {
        // 提取知识点内容（去除【知识点】标记）
        const knowledgeMatch = paragraph.match(/【知识点】(.*?)(?=<\/|$)/);
        if (knowledgeMatch && knowledgeMatch[1]) {
          const knowledgeText = knowledgeMatch[1].trim();
          // 如果有多个知识点（用逗号、顿号、分号、空格等分隔），则分割成数组
          currentKnowledgePoints = knowledgeText
            .split(/[,，、；;\s]+/g)
            .map(k => k.trim())
            .filter(k => k);
        } else {
          // 尝试从纯文本中提取
          const plainTextMatch = plainText.match(/【知识点】\s*([^【]+)/);
          if (plainTextMatch && plainTextMatch[1]) {
            const plainKnowledgeText = plainTextMatch[1].trim();
            currentKnowledgePoints = plainKnowledgeText
              .split(/[,，、；;\s]+/g)
              .map(k => k.trim())
              .filter(k => k);
          }
        }
        console.log('找到知识点标记(主解析):', currentKnowledgePoints);
        continue;
      }

      // 处理难度标记
      if (paragraph.includes('【难度】')) {
        // 提取难度内容（去除【难度】标记）
        const difficultyMatch = paragraph.match(/【难度】(.*?)(?=<\/|$)/);
        if (difficultyMatch && difficultyMatch[1]) {
          currentDifficulty = difficultyMatch[1].trim();
        }
        console.log('找到难度标记:', currentDifficulty);
        continue;
      }

      // 处理题目分层标记
      if (paragraph.includes('【题目分层】')) {
        // 提取题目分层内容（去除【题目分层】标记）
        const levelMatch = paragraph.match(/【题目分层】(.*?)(?=<\/|$)/);
        if (levelMatch && levelMatch[1]) {
          currentLevel = levelMatch[1].trim();
        }
        console.log('找到题目分层标记:', currentLevel);
        continue;
      }

      // 处理页码标记
      if (paragraph.includes('【页码】')) {
        // 提取页码内容（去除【页码】标记）
        const pageMatch = paragraph.match(/【页码】(.*?)(?=<\/|$)/);
        if (pageMatch && pageMatch[1]) {
          const pageText = pageMatch[1].trim();
          // 尝试将页码转换为数字
          const pageNumber = parseInt(pageText);
          if (!isNaN(pageNumber)) {
            currentPageNumber = pageNumber;
          }
        }
        console.log('找到页码标记:', currentPageNumber);
        continue;
      }

      // 处理答题区标记
      if (paragraph.includes('【答题区】')) {
        // 提取答题区内容（去除【答题区】标记）
        const areaMatch = paragraph.match(/【答题区】(.*?)(?=<\/|$)/);
        if (areaMatch && areaMatch[1]) {
          const areaText = areaMatch[1].trim();
          // 尝试将答题区转换为数字
          const areaNumber = parseInt(areaText);
          if (!isNaN(areaNumber)) {
            currentAnswerArea = areaNumber;
          }
        }
        console.log('找到答题区标记:', currentAnswerArea);
        continue;
      }

      // 处理普通段落
      if (paragraph.trim()) {
        if (inContent) {
          // 检查是否包含A选项但不是以A开头
          if (
            plainText.includes('A.') ||
            plainText.includes('A、') ||
            plainText.includes('A．') ||
            plainText.includes('A:') ||
            plainText.includes('A：') ||
            plainText.includes('A ')
          ) {
            // 尝试分割题干和选项
            let contentPart = '';
            let optionsPart = '';

            // 查找A选项的位置
            const aPos = Math.min(
              plainText.indexOf('A.') !== -1
                ? plainText.indexOf('A.')
                : Number.MAX_SAFE_INTEGER,
              plainText.indexOf('A、') !== -1
                ? plainText.indexOf('A、')
                : Number.MAX_SAFE_INTEGER,
              plainText.indexOf('A．') !== -1
                ? plainText.indexOf('A．')
                : Number.MAX_SAFE_INTEGER,
              plainText.indexOf('A:') !== -1
                ? plainText.indexOf('A:')
                : Number.MAX_SAFE_INTEGER,
              plainText.indexOf('A：') !== -1
                ? plainText.indexOf('A：')
                : Number.MAX_SAFE_INTEGER,
              plainText.indexOf('A ') !== -1
                ? plainText.indexOf('A ')
                : Number.MAX_SAFE_INTEGER
            );

            if (aPos !== Number.MAX_SAFE_INTEGER) {
              // 在HTML中找到对应位置
              const htmlText = paragraph;
              let htmlPos = 0;
              let plainPos = 0;
              let inTag = false;

              // 遍历HTML，找到纯文本中A选项对应的HTML位置
              for (let j = 0; j < htmlText.length; j++) {
                if (htmlText[j] === '<') {
                  // 进入标签
                  inTag = true;
                } else if (htmlText[j] === '>') {
                  // 退出标签
                  inTag = false;
                } else if (!inTag) {
                  // 只有不在标签内的文本才计数
                  if (plainPos === aPos) {
                    htmlPos = j;
                    break;
                  }
                  plainPos++;
                }
              }

              // 分割HTML
              contentPart = htmlText.substring(0, htmlPos).trim();
              optionsPart = htmlText.substring(htmlPos).trim();

              // 更新题干和选项
              if (contentPart) {
                currentContent += contentPart;
              }

              if (optionsPart) {
                inContent = false;
                inOptions = true;
                // 检查是否是完整的A选项
                const optionMatch = optionsPart
                  .replace(/<[^>]*>/g, '')
                  .trim()
                  .match(optionRegex);
                if (optionMatch && optionMatch[1] === 'A') {
                  // 如果是完整的A选项，直接添加
                  currentOptions = [optionsPart];
                  console.log(
                    '分割出完整A选项:',
                    optionsPart.replace(/<[^>]*>/g, '').trim()
                  );
                } else {
                  // 如果不是完整的A选项，可能是选项的一部分，添加到选项数组
                  currentOptions.push(optionsPart);
                  console.log(
                    '分割出部分选项:',
                    optionsPart.replace(/<[^>]*>/g, '').trim()
                  );
                }
              }
            } else {
              // 如果无法分割，则整段作为题干
              currentContent += paragraph;
            }
          } else {
            // 普通题干内容
            currentContent += paragraph;
          }
        } else if (inOptions) {
          // 如果在选项部分遇到非选项格式的段落，可能是选项的延续
          if (currentOptions.length > 0) {
            // 检查是否可能是下一个选项的开始（没有字母标识但包含选项内容）
            const trimmedText = plainText.trim();
            if (trimmedText.length > 0 && !optionRegex.test(trimmedText)) {
              // 添加到最后一个选项
              currentOptions[currentOptions.length - 1] += paragraph;
              console.log(
                '添加选项延续内容:',
                trimmedText.substring(0, Math.min(20, trimmedText.length)) +
                  '...'
              );
            } else {
              // 可能是格式问题导致的新选项，但没有被正确识别
              // 尝试查找是否包含B-Z的选项标识
              const letterMatch = trimmedText.match(/^\s*([B-Z])[.、．:：\s]/);
              if (letterMatch) {
                // 找到了选项标识，作为新选项添加
                currentOptions.push(paragraph);
                console.log(
                  '添加可能的选项:',
                  letterMatch[1],
                  trimmedText.substring(0, Math.min(20, trimmedText.length)) +
                    '...'
                );
              } else {
                // 没有找到选项标识，作为上一个选项的延续
                currentOptions[currentOptions.length - 1] += paragraph;
              }
            }
          }
        } else if (inAnswer) {
          // 答案的延续
          currentAnswer += paragraph;
        } else if (inExplanation) {
          // 解析的延续
          currentExplanation += paragraph;
        }
      }
    }

    // 保存最后一个题目
    if (currentQuestionType && (currentContent || currentOptions.length > 0)) {
      // 保存当前题目
      this.saveCurrentQuestion(
        questions,
        currentQuestionType,
        currentContent,
        currentOptions,
        currentAnswer,
        currentExplanation,
        currentKnowledgePoints,
        currentDifficulty,
        currentLevel,
        currentPageNumber,
        currentAnswerArea
      );
    }

    return questions;
  }

  /**
   * 校验试题
   * @param question 试题
   * @returns 是否有效
   */
  private validateQuestion(question: Question): boolean {
    // 检查必填项
    if (!question.type || !question.content || !question.answer) {
      return false;
    }

    return true;
  }

  /**
   * 将选项文本数组转换为结构化的选项对象数组
   * @param optionsArray 选项文本数组
   * @returns 结构化的选项对象数组
   */
  private convertOptionsToStructured(optionsArray: string[]): QuestionOption[] {
    const structuredOptions: QuestionOption[] = [];

    // 选项的正则表达式 - 匹配以A-Z开头，后跟.、：或空格的文本
    const optionRegex = /^\s*([A-Z])[.、．:：\s]/;

    for (const option of optionsArray) {
      // 移除HTML标签获取纯文本，用于识别选项标识
      const plainText = this.stripHtml(option).trim();
      const optionMatch = plainText.match(optionRegex);

      if (optionMatch && optionMatch[1]) {
        // 提取选项标识（A, B, C, D等）
        const key = optionMatch[1];

        // 保留原始HTML格式的选项内容并清除原内容中开头部分的A. B. C. D.
        const content = option.replace(optionMatch[0], '');

        structuredOptions.push({
          [key]: content,
        });
      } else {
        // 如果无法识别选项标识，使用索引作为标识
        const index = structuredOptions.length;
        const key = String.fromCharCode(65 + index); // 65是ASCII码中的'A'
        structuredOptions.push({
          [key]: option,
        });
      }
    }

    return structuredOptions;
  }

  /**
   * 创建题目对象
   * 处理答案和解析中的标记，提取元数据，并创建标准化的题目对象
   */
  private createQuestionObject(
    questionType: string,
    content: string,
    options: string[],
    answer: string,
    explanation: string,
    knowledgePoints: string[],
    difficulty: string,
    level: string,
    pageNumber: number | null,
    answerArea: number | null
  ): Question {
    // 从答案和解析中提取其他信息，并获取清理后的答案和解析
    const additionalInfo = this.extractAdditionalInfo(answer, explanation);

    // 更新元数据 - 合并主解析和附加信息中的知识点
    let updatedKnowledgePoints: string[] = [];

    // 如果主解析和附加信息都有知识点，合并它们
    if (
      additionalInfo.knowledgePoints.length > 0 &&
      knowledgePoints.length > 0
    ) {
      // 合并两个数组并去重
      updatedKnowledgePoints = [
        ...new Set([...additionalInfo.knowledgePoints, ...knowledgePoints]),
      ];
      console.log('合并知识点:', updatedKnowledgePoints);
    }
    // 如果只有一个来源有知识点，使用那个来源
    else if (additionalInfo.knowledgePoints.length > 0) {
      updatedKnowledgePoints = additionalInfo.knowledgePoints;
    } else if (knowledgePoints.length > 0) {
      updatedKnowledgePoints = knowledgePoints;
    }

    const updatedDifficulty = additionalInfo.difficulty || difficulty;
    const updatedLevel = additionalInfo.level || level;
    const updatedPageNumber =
      additionalInfo.pageNumber !== null
        ? additionalInfo.pageNumber
        : pageNumber;
    const updatedAnswerArea =
      additionalInfo.answerArea !== null
        ? additionalInfo.answerArea
        : answerArea;

    // 将选项文本数组转换为结构化的选项对象数组
    const structuredOptions = this.convertOptionsToStructured(options);

    // 处理答案 - 对单选题、多选题和判断题，将答案转换为纯文本
    let processedAnswer = additionalInfo.cleanedAnswer;
    if (
      (
        [
          QuestionType.SINGLE_CHOICE,
          QuestionType.MULTIPLE_CHOICE,
          QuestionType.TRUE_FALSE,
        ] as string[]
      ).includes(questionType)
    ) {
      // 去除HTML标签，只保留纯文本
      const plainTextAnswer = this.stripHtml(
        additionalInfo.cleanedAnswer
      ).trim();

      // 对于单选题和多选题，可能需要进一步处理，例如提取选项标识（A、B、C、D等）
      if (questionType === QuestionType.SINGLE_CHOICE) {
        // 单选题通常只有一个字母作为答案
        const singleChoiceMatch = plainTextAnswer.match(/[A-Z]/);
        if (singleChoiceMatch) {
          processedAnswer = singleChoiceMatch[0];
        } else {
          processedAnswer = plainTextAnswer;
        }
      } else if (questionType === QuestionType.MULTIPLE_CHOICE) {
        // 多选题通常有多个字母作为答案，可能以各种方式分隔
        const multipleChoiceMatches = plainTextAnswer.match(/[A-Z]/g);
        if (multipleChoiceMatches) {
          processedAnswer = multipleChoiceMatches.join(',');
        } else {
          processedAnswer = plainTextAnswer;
        }
      } else if (questionType === QuestionType.TRUE_FALSE) {
        // 判断题通常是"正确"或"错误"，或者"√"或"×"
        if (
          plainTextAnswer.includes('正确') ||
          plainTextAnswer.includes('√') ||
          plainTextAnswer.includes('对') ||
          plainTextAnswer.includes('T') ||
          plainTextAnswer.includes('true')
        ) {
          processedAnswer = '正确';
        } else if (
          plainTextAnswer.includes('错误') ||
          plainTextAnswer.includes('×') ||
          plainTextAnswer.includes('错') ||
          plainTextAnswer.includes('F') ||
          plainTextAnswer.includes('false')
        ) {
          processedAnswer = '错误';
        } else {
          processedAnswer = plainTextAnswer;
        }
      }
    }

    // 创建题目对象
    return {
      type: questionType,
      content: content.replace(/【题干】/g, '').trim(),
      options: structuredOptions, // 使用结构化的选项对象数组
      images: [],
      raw: '',
      answer: processedAnswer,
      explanation: additionalInfo.cleanedExplanation,
      knowledgePoints:
        updatedKnowledgePoints.length > 0 ? updatedKnowledgePoints : undefined,
      difficulty: updatedDifficulty || undefined,
      level: updatedLevel || undefined,
      pageNumber: updatedPageNumber !== null ? updatedPageNumber : undefined,
      answerArea: updatedAnswerArea !== null ? updatedAnswerArea : undefined,
    };
  }

  /**
   * 保存当前题目
   * 创建题目对象并添加到题目数组中
   */
  private saveCurrentQuestion(
    questions: Question[],
    questionType: string,
    content: string,
    options: string[],
    answer: string,
    explanation: string,
    knowledgePoints: string[],
    difficulty: string,
    level: string,
    pageNumber: number | null,
    answerArea: number | null
  ): void {
    // 创建题目对象
    const question = this.createQuestionObject(
      questionType,
      content,
      options,
      answer,
      explanation,
      knowledgePoints,
      difficulty,
      level,
      pageNumber,
      answerArea
    );

    // 验证并保存题目
    if (this.validateQuestion(question)) {
      // 添加日志，显示题目的知识点
      if (question.knowledgePoints && question.knowledgePoints.length > 0) {
        console.log(
          `题目 "${question.content.substring(0, 30)}..." 的知识点:`,
          question.knowledgePoints
        );
      } else {
        console.log(
          `题目 "${question.content.substring(0, 30)}..." 没有知识点`
        );
      }

      questions.push(question);
    }
  }

  /**
   * 重置题目状态
   * 返回初始化的状态对象
   */
  private resetQuestionState(): {
    content: string;
    options: string[];
    answer: string;
    explanation: string;
    knowledgePoints: string[];
    difficulty: string;
    level: string;
    pageNumber: number | null;
    answerArea: number | null;
    inContent: boolean;
    inOptions: boolean;
    inAnswer: boolean;
    inExplanation: boolean;
  } {
    // 返回重置后的状态
    return {
      content: '',
      options: [],
      answer: '',
      explanation: '',
      knowledgePoints: [],
      difficulty: '',
      level: '',
      pageNumber: null,
      answerArea: null,
      inContent: false,
      inOptions: false,
      inAnswer: false,
      inExplanation: false,
    };
  }

  /**
   * 从答案和解析中提取其他信息，并返回清理后的答案和解析
   */
  private extractAdditionalInfo(
    answer: string,
    explanation: string
  ): {
    knowledgePoints: string[];
    difficulty: string;
    level: string;
    pageNumber: number | null;
    answerArea: number | null;
    cleanedAnswer: string;
    cleanedExplanation: string;
  } {
    // 初始化返回值
    const result = {
      knowledgePoints: [] as string[],
      difficulty: '',
      level: '',
      pageNumber: null as number | null,
      answerArea: null as number | null,
      cleanedAnswer: '',
      cleanedExplanation: '',
    };

    // 处理答案部分
    const answerParagraphs = this.splitIntoParagraphs(answer);
    const realAnswerParagraphs: string[] = [];

    // 遍历答案中的段落
    for (const paragraph of answerParagraphs) {
      const plainText = this.stripHtml(paragraph);

      // 判断段落类型
      if (plainText.includes('【答案】')) {
        // 这是答案段落，提取实际答案内容
        const answerContent = paragraph.replace(/【答案】/g, '');
        realAnswerParagraphs.push(answerContent);
      } else if (plainText.includes('【题目分层】')) {
        // 这是题目分层段落，提取题目分层
        const levelMatch = plainText.match(/【题目分层】\s*([^\s【]+)/);
        if (levelMatch && levelMatch[1]) {
          // 直接使用纯文本
          result.level = this.stripHtml(levelMatch[1]).trim();
          console.log('提取到题目分层:', result.level);
        }
      } else if (plainText.includes('【难度】')) {
        // 这是难度段落，提取难度
        const difficultyMatch = plainText.match(/【难度】\s*([^\s【]+)/);
        if (difficultyMatch && difficultyMatch[1]) {
          result.difficulty = difficultyMatch[1].trim();
          console.log('提取到难度:', result.difficulty);
        }
      } else if (plainText.includes('【知识点】')) {
        // 这是知识点段落，提取知识点
        const knowledgeMatch = plainText.match(/【知识点】\s*([^【]+)/);
        if (knowledgeMatch && knowledgeMatch[1]) {
          const knowledgeText = knowledgeMatch[1].trim();
          // 使用更灵活的分隔符，包括逗号、顿号、分号、空格等
          const points = knowledgeText
            .split(/[,，、；;\s]+/g)
            .map(k => k.trim())
            .filter(k => k);
          if (points.length > 0) {
            result.knowledgePoints = points;
            console.log('提取到知识点(答案部分):', points);
          }
        } else {
          // 尝试直接从HTML中提取知识点内容
          const htmlMatch = paragraph.match(/【知识点】(.*?)(?=<\/|$)/);
          if (htmlMatch && htmlMatch[1]) {
            const knowledgeHtml = htmlMatch[1].trim();
            const knowledgeText = this.stripHtml(knowledgeHtml).trim();
            const points = knowledgeText
              .split(/[,，、；;\s]+/g)
              .map(k => k.trim())
              .filter(k => k);
            if (points.length > 0) {
              result.knowledgePoints = points;
              console.log('提取到知识点(答案部分-HTML):', points);
            }
          }
        }
      } else {
        // 这是普通段落，属于答案内容
        realAnswerParagraphs.push(paragraph);
      }
    }

    // 组合实际答案内容
    result.cleanedAnswer = realAnswerParagraphs.join('');

    // 处理解析部分
    const explanationParagraphs = this.splitIntoParagraphs(explanation);
    const realExplanationParagraphs: string[] = [];

    // 遍历解析中的段落
    for (const paragraph of explanationParagraphs) {
      const plainText = this.stripHtml(paragraph);

      // 判断段落类型
      if (plainText.includes('【解析】')) {
        // 这是解析段落，提取实际解析内容
        const explanationContent = paragraph.replace(/【解析】/g, '');
        realExplanationParagraphs.push(explanationContent);
      } else if (plainText.includes('【页码】')) {
        // 这是页码段落，提取页码
        const pageMatch = plainText.match(/【页码】\s*(\d+)/);
        if (pageMatch && pageMatch[1]) {
          const page = parseInt(pageMatch[1]);
          if (!isNaN(page)) {
            result.pageNumber = page;
            console.log('提取到页码:', page);
          }
        }
      } else if (plainText.includes('【答题区】')) {
        // 这是答题区段落，提取答题区
        const areaMatch = plainText.match(/【答题区】\s*(\d+)/);
        if (areaMatch && areaMatch[1]) {
          const area = parseInt(areaMatch[1]);
          if (!isNaN(area)) {
            result.answerArea = area;
            console.log('提取到答题区:', area);
          }
        }
      } else if (plainText.includes('【知识点】')) {
        // 这是知识点段落，提取知识点
        const knowledgeMatch = plainText.match(/【知识点】\s*([^【]+)/);
        if (knowledgeMatch && knowledgeMatch[1]) {
          const knowledgeText = knowledgeMatch[1].trim();
          // 使用更灵活的分隔符，包括逗号、顿号、分号、空格等
          const points = knowledgeText
            .split(/[,，、；;\s]+/g)
            .map(k => k.trim())
            .filter(k => k);
          if (points.length > 0) {
            result.knowledgePoints = points;
            console.log('提取到知识点(解析部分):', points);
          }
        } else {
          // 尝试直接从HTML中提取知识点内容
          const htmlMatch = paragraph.match(/【知识点】(.*?)(?=<\/|$)/);
          if (htmlMatch && htmlMatch[1]) {
            const knowledgeHtml = htmlMatch[1].trim();
            const knowledgeText = this.stripHtml(knowledgeHtml).trim();
            const points = knowledgeText
              .split(/[,，、；;\s]+/g)
              .map(k => k.trim())
              .filter(k => k);
            if (points.length > 0) {
              result.knowledgePoints = points;
              console.log('提取到知识点(解析部分-HTML):', points);
            }
          }
        }
      } else {
        // 这是普通段落，属于解析内容
        realExplanationParagraphs.push(paragraph);
      }
    }

    // 组合实际解析内容
    result.cleanedExplanation = realExplanationParagraphs.join('');

    return result;
  }

  /**
   * 将HTML按照<p>标签分割成段落数组
   */
  private splitIntoParagraphs(html: string): string[] {
    // 使用正则表达式匹配所有<p>标签及其内容
    const paragraphRegex = /<p[^>]*>.*?<\/p>/gs;
    const matches = html.match(paragraphRegex);

    return matches || [];
  }

  /**
   * 去除HTML标签，只保留文本内容
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '');
  }
}

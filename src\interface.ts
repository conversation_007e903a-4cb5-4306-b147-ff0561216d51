/**
 * 通用对象，对应类似字典的信息，如题型、知识点、作者、来源等
 */
export interface BaseObject {
  /**
   * ID，与code二选一
   */
  id?: number | string;
  /**
   * code，与id二选一
   */
  code?: string;
  /**
   * 名称，最大长度为 32
   */
  name: string;
}

export interface SystemQuestions {
  /**
   * 名称，必填，最大长度为 32，题干
   */
  name: string;

  /**
   * 选项
   */
  options?: string[];

  /**
   * 试题难度
   */
  difficulty: BaseObject;

  /**
   * 年级
   */
  grade?: BaseObject;

  /**
   * 教材版本
   */
  textbookVersion?: BaseObject;

  /**
   * 学段
   */
  gradeSection?: BaseObject;

  /**
   * 学科
   */
  subject?: BaseObject;

  /**
   * 单元
   */
  unit?: BaseObject;

  /**
   * 课时
   */
  period?: BaseObject;

  /**
   * 最底层节点
   */
  catalog?: BaseObject;

  /**
   * 册次
   */
  volume?: BaseObject;

  /**
   * 基础题型
   */
  baseType: BaseObject;

  /**
   * 试题扩展题型
   */
  type?: BaseObject;

  /**
   * 作者
   */
  author?: BaseObject;

  /**
   * 来源
   */
  source?: BaseObject;

  /**
   * 年份，最大长度为 64
   */
  year?: string;

  /**
   * 分层
   */
  tier?: BaseObject;

  /**
   * 认知层次text
   */
  cognitiveHierarchy?: BaseObject;

  /**
   * 核心素养text
   */
  coreQuality?: BaseObject;

  /**
   * 考察能力text
   */
  investigationAbility?: BaseObject;

  /**
   * 知识点
   */
  points?: BaseObject[];

  /**
   * 类题标签
   */
  tags?: BaseObject[];

  /**
   * 答案
   */
  answer?: string;

  /**
   * 解析
   */
  analysis?: string;

  /**
   * 页码
   */
  pagination?: number;

  /**
   * 父题干
   */
  parentStem?: string;

  /**
   * 父题ID，最大长度为 64
   */
  pid?: string | number;

  /**
   * 是否是组合题
   */
  isCompose?: boolean;

  /**
   * 分类
   */
  classification?: string;

  /**
   * 所在表名称，最大长度为 64
   */
  tableName?: string;

  /** 试题来源id */
  sourceId?: string;

  /** 标签 */
  label?: string;

  /**
   * 企业code
   */
  enterpriseCode?: string;

  /**
   * 建议时长，单位秒
   */
  duration?: number;

  /**
   *  是否导入
   */
  isImported?: boolean;

  /**
   * 是否生效
   */
  isEffective?: boolean;

  /**
   * 试题题干
   */
  stem?: string;
}

export const testBody = {
  stem: '下面对有新鲜感的词语的理解，错误的一项是',
  duration: 1,
  year: '2025',
  points: [
    {
      name: '100以内数的进位加法、退位减法',
      id: '314',
    },
  ],
  name: '<p><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt;">下面对有</span><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt; text-emphasis: filled currentcolor; text-emphasis-position: under right;" data-emphasis-mark="dot">新鲜感</span><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt;">的词语的理解，错误的一项是</span></p>',
  options: [
    {
      A: '<p><span style="color: #000000; font-family: \'Times New Roman\'; font-size: 14.6667px; white-space-collapse: preserve;">边疆（靠近国界的领土）</span></p>',
    },
    {
      B: '<p><span style="color: #000000; font-family: \'Times New Roman\'; font-size: 14.6667px; white-space-collapse: preserve;">绚丽多彩（各种各样的色彩灿烂美丽）</span></p>',
    },
    {
      C: '<p><span style="color: #000000; font-family: \'Times New Roman\'; font-size: 14.6667px; white-space-collapse: preserve;">坪坝（高高低低的小路）</span></p>',
    },
    {
      D: '<p><span style="color: #000000; font-family: \'Times New Roman\'; font-size: 14.6667px; white-space-collapse: preserve;">坪坝（高高低低的大路）</span></p>',
    },
  ],
  answer: 'A',
  analysis:
    '<p><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt;">【分析】A、B、D正确；</span><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt;"> C.&ldquo;坪坝&rdquo;的意思是平坦的场地，错误。</span><br style="color: #000000; font-family: \'Microsoft YaHei\'; font-size: medium; white-space-collapse: preserve;" /><span style="color: #000000; white-space-collapse: preserve; font-family: \'Times New Roman\'; font-size: 11pt;">故答案为：C</span></p>\n<p><span style="color: #000000; font-family: \'Times New Roman\'; font-size: 14.6667px; white-space-collapse: preserve;">【点评】这道题考查了词语的理解，要根据平时的积累或联系上下文或查阅工具书来分析和选择。</span></p>',
  difficulty: {
    name: '中等',
    code: 'MEDIUM',
  },
  tier: {
    name: '基础',
    code: 'JI_CHU',
  },
  cognitiveHierarchy: {
    name: '创造',
    code: 'CHUANG_ZAO',
  },
  coreQuality: {
    name: '文化自信',
    code: 'WEN_HUA_ZI_XIN',
  },
  investigationAbility: {
    name: '基础型',
    code: 'KAO_CHA_NENG_LI',
  },
  baseType: {
    name: '单选题',
    code: 'SINGLE_CHOICE',
  },
  type: {
    name: '单选题',
    code: 'DX_KZ',
  },
  author: {
    id: 1,
    name: '系统管理员',
  },
  enterpriseCode: '0001',
  grade: {
    name: '三年级',
    code: '3',
  },
  subject: {
    name: '数学',
    id: 4,
  },
  textbookVersion: {
    name: '北师大版',
    id: 3,
  },
  volume: {
    name: '上册',
    id: 11,
  },
  gradeSection: {
    name: '小学',
    code: 'GRADE_PRIMARY',
  },
  catalog: {
    name: '小熊购物',
    id: 231,
  },
};

import {
  Provide,
  Config,
  Inject,
  retryWithAsync,
  MidwayRetryExceededMaxTimesError,
} from '@midwayjs/core';
import { QuestionType, Question } from './word-parser.service';
import { HttpService } from '@midwayjs/axios';

/**
 * NLP处理结果接口
 */
export interface NLPResult {
  success: boolean;
  message?: string;
  data?: any;
}

/**
 * 知识点接口
 */
export interface KnowledgePoint {
  id: number;
  name: string;
  category: string;
  weight: number;
}

/**
 * 相似题检测结果接口
 */
export interface SimilarityResult {
  questionId: number;
  similarity: number; // 相似度，0-1之间
  matchedText: string; // 匹配的文本
}

/**
 * NLP服务配置接口
 */
export interface NLPConfig {
  // API配置
  apiEndpoint: string;
  apiKey: string;
  apiSecret?: string;

  // 速率限制配置
  rateLimit: {
    maxRequests: number; // 最大请求数
    perInterval: number; // 时间间隔(毫秒)
  };

  // 重试配置
  retry: {
    attempts: number; // 重试次数
    factor: number; // 重试间隔因子
    minTimeout: number; // 最小超时时间(毫秒)
    maxTimeout: number; // 最大超时时间(毫秒)
  };

  // 默认题型和知识点
  defaultQuestionTypes: string[];
  defaultKnowledgePoints: KnowledgePoint[];
}

@Provide()
export class NLPHelperService {
  @Config('nlp')
  private nlpConfig: NLPConfig;

  @Inject()
  private httpService: HttpService;

  // 请求计数器和时间戳
  private requestCount = 0;
  private requestTimestamp: number = Date.now();

  /**
   * 初始化函数
   */
  async init() {
    // 检查配置是否存在
    if (!this.nlpConfig) {
      console.warn('NLP配置不存在，使用默认配置');
      this.nlpConfig = {
        apiEndpoint: 'https://nlp-api.example.com',
        apiKey: 'default-key',
        rateLimit: {
          maxRequests: 10,
          perInterval: 60000, // 1分钟
        },
        retry: {
          attempts: 3,
          factor: 2,
          minTimeout: 1000,
          maxTimeout: 10000,
        },
        defaultQuestionTypes: [
          '单选题',
          '多选题',
          '判断题',
          '填空题',
          '简答题',
          '计算题',
        ],
        defaultKnowledgePoints: [],
      };
    }
  }

  /**
   * 检查API速率限制
   * @returns 是否可以发送请求
   */
  private checkRateLimit(): boolean {
    const now = Date.now();
    const { maxRequests, perInterval } = this.nlpConfig.rateLimit;

    // 重置计数器
    if (now - this.requestTimestamp > perInterval) {
      this.requestCount = 0;
      this.requestTimestamp = now;
      return true;
    }

    // 检查是否超过限制
    if (this.requestCount >= maxRequests) {
      return false;
    }

    this.requestCount++;
    return true;
  }

  /**
   * 发送NLP API请求（带重试机制）
   * @param endpoint API端点
   * @param data 请求数据
   * @returns API响应
   */
  private async sendRequest(endpoint: string, data: any): Promise<any> {
    // 检查速率限制
    if (!this.checkRateLimit()) {
      throw new Error('API请求超过速率限制，请稍后再试');
    }

    // 创建请求函数
    const doRequest = async () => {
      console.log('尝试发送NLP请求');

      try {
        const response = await this.httpService.post(
          `${this.nlpConfig.apiEndpoint}${endpoint}`,
          data,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${this.nlpConfig.apiKey}`,
            },
            timeout: 10000, // 10秒超时
          }
        );

        return response.data;
      } catch (error) {
        // 如果是客户端错误(4xx)，则不重试
        if (
          error.response &&
          error.response.status >= 400 &&
          error.response.status < 500
        ) {
          console.error(`NLP API客户端错误: ${error.message}，不再重试`);
          // 对于客户端错误，直接抛出不重试
          throw new Error(`API请求失败: ${error.message}`);
        }

        // 服务器错误(5xx)或网络错误，允许重试
        console.error(`NLP API请求错误: ${error.message}，准备重试`);
        throw error;
      }
    };

    // 使用Midway的重试机制
    try {
      // 包装请求函数，添加重试逻辑
      const requestWithRetry = retryWithAsync(
        doRequest,
        this.nlpConfig.retry.attempts - 1,
        {
          retryInterval: this.nlpConfig.retry.minTimeout,
        }
      );

      return await requestWithRetry();
    } catch (error) {
      // 处理重试超过最大次数的错误
      if (error instanceof MidwayRetryExceededMaxTimesError) {
        console.error(`NLP API请求重试次数已达上限: ${error.message}`);
        throw error.cause || error;
      }
      throw error;
    }
  }

  /**
   * 自动判断题型
   * @param questionText 题目文本
   * @returns 判断结果
   */
  async detectQuestionType(questionText: string): Promise<NLPResult> {
    try {
      // 调用NLP API进行题型判断
      const result = await this.sendRequest('/classify/question-type', {
        text: questionText,
        candidateTypes: this.nlpConfig.defaultQuestionTypes,
      });

      return {
        success: true,
        data: {
          type: result.type || QuestionType.UNKNOWN,
          confidence: result.confidence || 0,
        },
      };
    } catch (error) {
      console.error('题型判断失败:', error);
      return {
        success: false,
        message: `题型判断失败: ${error.message}`,
      };
    }
  }

  /**
   * 提取知识点
   * @param questionText 题目文本
   * @returns 知识点列表
   */
  async extractKnowledgePoints(questionText: string): Promise<NLPResult> {
    try {
      // 调用NLP API提取知识点
      const result = await this.sendRequest('/extract/knowledge-points', {
        text: questionText,
        candidatePoints: this.nlpConfig.defaultKnowledgePoints.map(p => p.name),
      });

      return {
        success: true,
        data: result.knowledgePoints || [],
      };
    } catch (error) {
      console.error('知识点提取失败:', error);
      return {
        success: false,
        message: `知识点提取失败: ${error.message}`,
      };
    }
  }

  /**
   * 检测相似题目
   * @param question 当前题目
   * @param threshold 相似度阈值(0-1之间)
   * @returns 相似题目列表
   */
  async detectSimilarQuestions(
    question: Question,
    threshold = 0.8
  ): Promise<NLPResult> {
    try {
      // 调用NLP API检测相似题目
      const result = await this.sendRequest('/detect/similar-questions', {
        text: question.content,
        type: question.type,
        threshold: threshold,
      });

      return {
        success: true,
        data: result.similarQuestions || [],
      };
    } catch (error) {
      console.error('相似题检测失败:', error);
      return {
        success: false,
        message: `相似题检测失败: ${error.message}`,
      };
    }
  }

  /**
   * 批量处理题目（题型判断+知识点提取）
   * @param questions 题目列表
   * @returns 处理结果
   */
  async batchProcessQuestions(questions: Question[]): Promise<NLPResult> {
    try {
      const results = [];

      for (const question of questions) {
        // 如果题型未知，尝试判断题型
        if (question.type === QuestionType.UNKNOWN) {
          const typeResult = await this.detectQuestionType(question.content);
          if (typeResult.success) {
            question.type = typeResult.data.type;
          }
        }

        // 提取知识点
        const pointsResult = await this.extractKnowledgePoints(
          question.content
        );

        results.push({
          questionId: question.id,
          originalType: question.type,
          detectedType: question.type,
          knowledgePoints: pointsResult.success ? pointsResult.data : [],
        });
      }

      return {
        success: true,
        data: results,
      };
    } catch (error) {
      console.error('批量处理题目失败:', error);
      return {
        success: false,
        message: `批量处理题目失败: ${error.message}`,
      };
    }
  }
}

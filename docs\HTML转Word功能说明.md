# HTML转Word功能说明

## 功能概述

本功能是对现有Word转HTML功能的逆向实现，可以将结构化的HTML试题内容转换为Word文档进行导出。主要特点包括：

- 支持将HTML内容转换为标准的.docx格式文档
- 保留原始HTML中的格式、样式、图片、表格和公式等元素
- 提供API接口和示例代码，方便集成到现有系统中
- 支持自定义文档属性（标题、作者、页边距、方向等）

## 使用方法

### 1. 通过API接口使用

系统提供了两个API接口用于HTML转Word功能：

#### 1.1 直接下载Word文档

```
POST /word-export/html-to-word
```

请求参数：

```json
{
  "htmlContent": "<p>HTML内容</p>",
  "options": {
    "title": "文档标题",
    "author": "作者",
    "fileName": "输出文件名.docx",
    "margins": {
      "top": 1440,
      "right": 1440,
      "bottom": 1440,
      "left": 1440
    },
    "orientation": "portrait" // 或 "landscape"
  }
}
```

响应：直接返回Word文档的二进制流，浏览器会自动下载。

#### 1.2 保存到服务器

```
POST /word-export/save-to-server
```

请求参数：

```json
{
  "htmlContent": "<p>HTML内容</p>",
  "outputPath": "/path/to/output.docx",
  "options": {
    "title": "文档标题",
    "author": "作者",
    "fileName": "输出文件名.docx",
    "margins": {
      "top": 1440,
      "right": 1440,
      "bottom": 1440,
      "left": 1440
    },
    "orientation": "portrait" // 或 "landscape"
  }
}
```

响应：

```json
{
  "success": true,
  "message": "Word文档保存成功",
  "data": {
    "filePath": "/path/to/output.docx"
  }
}
```

### 2. 在代码中使用

可以直接在代码中使用`WordExportService`进行HTML到Word的转换：

```typescript
import { WordExportService } from '../service/word-export.service';

// 创建服务实例
const wordExportService = new WordExportService();

// 将HTML转换为Word文档Buffer
const docxBuffer = await wordExportService.exportHtmlToWord(
  htmlContent,
  {
    title: '文档标题',
    author: '作者',
    orientation: 'portrait'
  }
);

// 或者直接保存为文件
const savedPath = await wordExportService.exportHtmlToWordFile(
  htmlContent,
  '/path/to/output.docx',
  {
    title: '文档标题',
    author: '作者',
    orientation: 'portrait'
  }
);
```

### 3. 运行示例代码

项目中提供了示例代码，可以直接运行测试：

```bash
# 进入项目目录
cd question-import-word

# 编译TypeScript
npm run build

# 运行示例
node dist/example/word-export-example.js
```

示例代码会读取`examples/test.html`文件，将其转换为Word文档并保存为`examples/test-export.docx`。

**快速验证方法：**

您也可以使用以下命令直接运行 TypeScript 示例文件，无需先进行编译：

```bash
node --require ts-node/register src/example/word-export-example.ts
```

或者使用 npx 方式：

```bash
npx ts-node src/example/word-export-example.ts
```

## 实现原理

### 技术栈

- 核心库：html-to-docx
- DOM解析：jsdom
- 文件操作：fs、path

### 处理流程

1. **预处理HTML**：
   - 解析HTML DOM结构
   - 处理图片（确保base64编码正确）
   - 处理表格（添加必要的属性和样式）
   - 处理数学公式（确保公式正确显示）
   - 处理行内样式（转换为Word支持的格式）

2. **转换为Word**：
   - 使用html-to-docx库将处理后的HTML转换为Word文档
   - 应用文档属性（标题、作者、页边距、方向等）

3. **输出处理**：
   - 返回文档Buffer或保存为文件
   - 设置正确的MIME类型和文件名

### 注意事项

1. **图片处理**：
   - 支持base64编码的内联图片
   - 外部图片链接可能无法正确转换

2. **表格处理**：
   - 自动添加边框和padding
   - 设置表格宽度为100%以适应页面

3. **公式处理**：
   - 支持以图片形式存在的公式
   - 非图片形式的公式可能无法正确转换

4. **样式处理**：
   - 支持基本的文本样式（字体、颜色、对齐方式等）
   - 复杂的CSS样式可能无法完全转换

## 限制和已知问题

1. 不支持所有CSS样式，只支持Word文档能够表示的基本样式
2. 复杂的布局可能无法完全保留
3. 某些特殊字体可能会被替换为标准字体
4. 非图片形式的数学公式可能无法正确显示

## 未来改进

1. 增强对复杂布局的支持
2. 改进公式处理，支持MathML或LaTeX格式的公式
3. 添加更多自定义选项（水印、页眉页脚等）
4. 优化图片处理，支持外部图片的自动下载和转换

## 参考资料

- [html-to-docx文档](https://www.npmjs.com/package/html-to-docx)
- [Word文档格式规范](https://docs.microsoft.com/en-us/office/open-xml/word-processing)
import { Document, Paragraph, Run, Packer } from 'docx';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 简单的docx库测试
 * 测试是否能正确处理包含空格的文本
 */
async function testSimpleDocx() {
  console.log('开始简单的docx测试...');

  // 创建包含空格的文本
  const texts = [
    ' 1. ',
    ' 2. ',
    '普通文本',
    '  前后都有空格  ',
    'A',
    'B',
    'C'
  ];

  const paragraphs: Paragraph[] = [];

  // 为每个文本创建一个段落
  texts.forEach((text, index) => {
    console.log(`创建段落 ${index}: "${text}"`);

    const run = new Run({ text });
    const paragraph = new Paragraph({ children: [run] });
    paragraphs.push(paragraph);
  });

  // 创建文档
  const doc = new Document({
    sections: [
      {
        properties: {},
        children: paragraphs,
      },
    ],
  });

  // 保存文档
  const outputPath = path.join(process.cwd(), 'testFiles', 'simple-docx-test.docx');

  try {
    const buffer = await Packer.toBuffer(doc);
    fs.writeFileSync(outputPath, buffer);
    console.log(`简单测试文档保存成功: ${outputPath}`);

    // 输出段落信息
    console.log(`\n创建的段落数量: ${paragraphs.length}`);
    paragraphs.forEach((paragraph, index) => {
      const paragraphAny = paragraph as any;
      console.log(`段落 ${index} 的所有属性:`, Object.keys(paragraphAny));

      // 尝试不同的属性名
      if (paragraphAny.children) {
        console.log(`段落 ${index}: 有children属性，长度: ${paragraphAny.children.length}`);
      } else if (paragraphAny._children) {
        console.log(`段落 ${index}: 有_children属性，长度: ${paragraphAny._children.length}`);
      } else if (paragraphAny.root) {
        console.log(`段落 ${index}: 有root属性`);
      } else {
        console.log(`段落 ${index}: 没有找到children相关属性`);
      }
    });

  } catch (error) {
    console.error('保存文档失败:', error);
  }
}

// 运行测试
testSimpleDocx().catch(console.error);

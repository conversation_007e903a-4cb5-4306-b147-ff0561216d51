# 文本处理工具 (text)

## 模块简介

`text` 模块专门用于处理Word文档中的文本样式和格式，提供了一系列函数来应用文本样式到HTML元素，确保Word文档中的文本格式在HTML中得到正确显示。

## 主要功能

- 处理文本样式（颜色、字体、大小等）
- 处理文本格式（粗体、斜体、下划线等）
- 支持从Word运行属性中提取样式
- 提供统一的样式应用接口

## 文件结构

```
text/
├── index.ts          # 文本处理主模块
└── README.md         # 本文档
```

## API说明

### 主要函数

#### `applyTextStyle(element: HTMLElement, style: TextStyle): void`

应用文本样式到HTML元素。

**参数:**
- `element`: HTML元素
- `style`: 文本样式对象

**样式对象属性:**
```typescript
interface TextStyle {
  bold?: boolean;         // 粗体
  italic?: boolean;       // 斜体
  underline?: boolean;    // 下划线
  strike?: boolean;       // 删除线
  color?: string;         // 颜色
  highlight?: string;     // 高亮颜色
  fontSize?: string;      // 字体大小
  fontFamily?: string;    // 字体
  verticalAlign?: string; // 垂直对齐
}
```

#### `applyTextStyleFromWordRun(span: HTMLSpanElement, rPr: Element): void`

从Word文档的运行属性中应用文本样式。

**参数:**
- `span`: HTML span元素
- `rPr`: 运行属性元素

## 文本样式处理流程

1. **提取样式信息**: 从Word文档的运行属性中提取样式信息
2. **创建样式对象**: 将提取的样式信息转换为样式对象
3. **应用样式**: 将样式应用到HTML元素

## 支持的文本样式

- **颜色**: 文本颜色
- **字体**: 字体名称
- **字体大小**: 字体大小（磅值）
- **粗体**: 文本加粗
- **斜体**: 文本倾斜
- **下划线**: 文本下划线
- **删除线**: 文本删除线
- **高亮**: 文本背景高亮
- **垂直对齐**: 上标、下标等

## 使用示例

### 使用样式对象

```typescript
import { applyTextStyle } from '../utils/text';

// 创建HTML元素
const span = document.createElement('span');
span.textContent = '示例文本';

// 应用样式
applyTextStyle(span, {
  bold: true,
  italic: true,
  color: '#FF0000',
  fontSize: '14pt',
  fontFamily: 'Arial'
});

console.log(span.outerHTML);
// 输出: <span style="font-weight: bold; font-style: italic; color: #FF0000; font-size: 14pt; font-family: Arial;">示例文本</span>
```

### 从Word运行属性中应用样式

```typescript
import { applyTextStyleFromWordRun } from '../utils/text';
import { DOMParser } from 'xmldom';

// 解析XML
const parser = new DOMParser();
const xml = `<w:rPr xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:b/>
  <w:i/>
  <w:color w:val="FF0000"/>
  <w:sz w:val="28"/>
  <w:rFonts w:ascii="Arial" w:eastAsia="宋体"/>
</w:rPr>`;
const doc = parser.parseFromString(xml, 'text/xml');
const rPr = doc.getElementsByTagName('w:rPr')[0];

// 创建HTML元素
const jsdom = require('jsdom');
const { JSDOM } = jsdom;
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
const document = dom.window.document;
const span = document.createElement('span');
span.textContent = '示例文本';

// 应用样式
applyTextStyleFromWordRun(span, rPr);

console.log(span.outerHTML);
```

## 注意事项

1. Word中的字体大小是磅值的两倍，需要进行转换
2. 优先使用eastAsia字体，因为主要处理中文文档
3. 样式是通过内联样式实现的，不使用CSS类
4. 对于复杂的文本格式，可能需要额外处理

import { Controller, Inject, Post, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WordExportService } from '../service/word-export.service';
import { Validate } from '@midwayjs/validate';

/**
 * Word导出控制器
 * 提供将HTML内容转换为Word文档的API接口
 */
@Controller('/word-export')
export class WordExportController {
  @Inject()
  ctx: Context;

  @Inject()
  wordExportService: WordExportService;

  /**
   * 将HTML内容转换为Word文档并返回下载
   * @param body 请求体，包含HTML内容和导出选项
   * @param ctx Koa上下文
   */
  @Post('/html-to-word')
  async exportHtmlToWord(
    @Body()
    body: {
      htmlContent: string;
      options?: {
        title?: string;
        author?: string;
        fileName?: string;
        margins?: {
          top?: number;
          right?: number;
          bottom?: number;
          left?: number;
        };
        orientation?: 'portrait' | 'landscape';
      };
    }
  ) {
    try {
      // 验证请求参数
      if (!body.htmlContent) {
        return {
          success: false,
          message: 'HTML内容不能为空',
        };
      }

      // 设置默认文件名
      const fileName = body.options?.fileName || '试题导出.docx';

      // 将HTML转换为Word文档
      const docxBuffer = await this.wordExportService.exportHtmlToWord(
        body.htmlContent,
        body.options
      );

      // 设置响应头，使浏览器下载文件
      this.ctx.set(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      );
      this.ctx.set(
        'Content-Disposition',
        `attachment; filename=${encodeURIComponent(fileName)}`
      );
      this.ctx.set('Content-Length', docxBuffer.length.toString());

      // 返回Word文档
      this.ctx.body = docxBuffer;
    } catch (error) {
      console.error('导出Word文档失败:', error);
      return {
        success: false,
        message: '导出Word文档失败: ' + (error.message || '未知错误'),
      };
    }
  }

  /**
   * 将HTML内容转换为Word文档并保存到服务器
   * @param body 请求体，包含HTML内容、输出路径和导出选项
   */
  @Post('/save-to-server')
  async saveHtmlToWordFile(
    @Body()
    body: {
      htmlContent: string;
      outputPath: string;
      options?: {
        title?: string;
        author?: string;
        fileName?: string;
        margins?: {
          top?: number;
          right?: number;
          bottom?: number;
          left?: number;
        };
        orientation?: 'portrait' | 'landscape';
      };
    }
  ) {
    try {
      // 验证请求参数
      if (!body.htmlContent) {
        return {
          success: false,
          message: 'HTML内容不能为空',
        };
      }

      if (!body.outputPath) {
        return {
          success: false,
          message: '输出路径不能为空',
        };
      }

      // 将HTML转换为Word文档并保存到服务器
      const savedPath = await this.wordExportService.exportHtmlToWordFile(
        body.htmlContent,
        body.outputPath,
        body.options
      );

      return {
        success: true,
        message: 'Word文档保存成功',
        data: {
          filePath: savedPath,
        },
      };
    } catch (error) {
      console.error('保存Word文档失败:', error);
      return {
        success: false,
        message: '保存Word文档失败: ' + (error.message || '未知错误'),
      };
    }
  }

  /**
   * 从本地HTML文件导出Word文档
   */
  @Post('/export/local')
  @Validate()
  async exportLocalHtml(
    @Body()
    body: {
      filePath: string;
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
    }
  ) {
    // 验证文件路径
    if (!body.filePath) {
      return {
        code: 400,
        message: '文件路径不能为空',
      };
    }

    // 验证文件是否存在
    const fs = require('fs');
    if (!fs.existsSync(body.filePath)) {
      return {
        code: 404,
        message: '文件不存在',
      };
    }

    // 验证文件扩展名
    const ext = body.filePath.toLowerCase().split('.').pop();
    if (ext !== 'html' && ext !== 'htm') {
      return {
        code: 400,
        message: '只支持HTML文件',
      };
    }

    try {
      // 导出Word文档
      const result = await this.wordExportService.exportLocalHtmlToTemp(
        body.filePath,
        {
          title: body.title,
          author: body.author,
          margins: body.margins,
          orientation: body.orientation,
        }
      );

      return {
        code: 200,
        message: '导出成功',
        data: {
          filePath: result,
        },
      };
    } catch (error) {
      return {
        code: 500,
        message: '导出失败: ' + error.message,
      };
    }
  }
}

# Utils 工具类目录结构说明

本目录包含了项目中所有的工具类，按照功能模块进行了重新组织，分为Word文档导入和导出两大类。

## 目录结构

```
src/utils/
├── word-import/          # Word文档导入相关工具
│   ├── docx/            # Word文档解析核心模块
│   │   ├── index.ts     # 模块主入口
│   │   ├── parser.ts    # XML解析和数据提取
│   │   ├── html-converter.ts # XML转HTML转换
│   │   └── README.md    # 模块说明文档
│   ├── formula/         # 数学公式处理模块
│   │   ├── index.ts     # 模块主入口
│   │   ├── docx-formula.ts # 公式HTML元素创建
│   │   ├── latex-renderer.ts # LaTeX渲染服务
│   │   ├── mathjax-formula.ts # MathJax公式处理
│   │   └── README.md    # 模块说明文档
│   ├── image/           # 图片处理模块
│   │   ├── index.ts     # 模块主入口
│   │   ├── docx-image.ts # Word图片处理
│   │   └── README.md    # 模块说明文档
│   ├── table/           # 表格处理模块
│   │   ├── index.ts     # 模块主入口
│   │   ├── docx-table.ts # Word表格处理
│   │   └── README.md    # 模块说明文档
│   └── text/            # 文本处理模块
│       ├── index.ts     # 模块主入口
│       └── README.md    # 模块说明文档
└── word-export/         # Word文档导出相关工具
    ├── html-parser.ts   # HTML解析器
    ├── image-processor.ts # 图片处理器
    ├── style-parser.ts  # 样式解析器
    └── text-processor.ts # 文本处理器
```

## 功能说明

### Word导入工具 (word-import/)

用于解析Word文档(.docx)并转换为HTML格式，保留原始格式和样式。

- **docx/**: 核心解析模块，负责解压Word文档、提取XML内容、解析样式信息
- **formula/**: 处理Word文档中的数学公式，支持OMML到LaTeX转换和公式渲染
- **image/**: 处理Word文档中的图片，支持多种图片格式和布局
- **table/**: 处理Word文档中的表格，保留表格结构和样式
- **text/**: 处理文本样式，包括字体、颜色、加粗、斜体等

### Word导出工具 (word-export/)

用于将HTML内容转换为Word文档(.docx)格式。

- **html-parser.ts**: 解析HTML DOM结构，转换为Word文档元素
- **image-processor.ts**: 处理HTML中的图片，转换为Word支持的格式
- **style-parser.ts**: 解析CSS样式，转换为Word样式格式
- **text-processor.ts**: 处理文本内容和样式，确保正确转换

## 使用方式

### 导入Word文档

```typescript
import { parseDocxWithColors } from '../utils/word-import/docx';

// 解析Word文档
const html = await parseDocxWithColors('path/to/document.docx');
```

### 导出Word文档

```typescript
import { WordExportService } from '../service/word-export.service';

// 创建导出服务实例
const exportService = new WordExportService();

// 将HTML转换为Word文档
const docxBuffer = await exportService.exportHtmlToWord(htmlContent, options);
```

## 重构说明

本次重构主要解决了以下问题：

1. **结构混乱**: 原来导入和导出相关的工具分散在不同层级
2. **职责不清**: 功能相似的工具没有统一组织
3. **维护困难**: 缺乏清晰的模块边界和依赖关系

重构后的优势：

1. **清晰的模块划分**: 按照导入/导出功能明确分类
2. **统一的目录层级**: 所有工具类都在utils目录下
3. **便于维护**: 相关功能集中管理，便于后续扩展和维护
4. **文档完善**: 每个模块都有详细的README说明

## 注意事项

1. 所有导入路径已更新为新的目录结构
2. 保持了原有功能的完整性，没有修改任何业务逻辑
3. 各模块之间的依赖关系保持不变
4. 所有现有的API接口保持兼容

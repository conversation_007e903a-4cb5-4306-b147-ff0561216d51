#!/usr/bin/env node

/**
 * 测试README中的命令说明是否正确
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 测试README中的命令说明...\n');

const commands = [
  {
    name: 'Node.js版本检查',
    command: 'node --version',
    expectSuccess: true
  },
  {
    name: 'npm版本检查', 
    command: 'npm --version',
    expectSuccess: true
  },
  {
    name: 'ts-node版本检查',
    command: 'npx ts-node --version',
    expectSuccess: true
  },
  {
    name: 'TypeScript编译检查',
    command: 'npx tsc --version',
    expectSuccess: true
  },
  {
    name: '项目编译测试',
    command: 'npm run build',
    expectSuccess: true
  },
  {
    name: 'Word导出示例（node --require方式）',
    command: 'node --require ts-node/register src/example/word-export-example.ts',
    expectSuccess: true
  },
  {
    name: 'Word导出示例（npx方式）',
    command: 'npx ts-node src/example/word-export-example.ts',
    expectSuccess: true
  }
];

let passedTests = 0;
let totalTests = commands.length;

for (const test of commands) {
  try {
    console.log(`🔍 测试: ${test.name}`);
    console.log(`   命令: ${test.command}`);
    
    const startTime = Date.now();
    const output = execSync(test.command, { 
      encoding: 'utf8',
      timeout: 30000,
      stdio: 'pipe'
    });
    const duration = Date.now() - startTime;
    
    if (test.expectSuccess) {
      console.log(`   ✅ 成功 (${duration}ms)`);
      if (output.trim()) {
        console.log(`   📄 输出: ${output.trim().split('\n')[0]}...`);
      }
      passedTests++;
    } else {
      console.log(`   ❌ 预期失败但成功了`);
    }
    
  } catch (error) {
    if (test.expectSuccess) {
      console.log(`   ❌ 失败: ${error.message.split('\n')[0]}`);
    } else {
      console.log(`   ✅ 预期失败`);
      passedTests++;
    }
  }
  
  console.log('');
}

// 检查输出文件
console.log('📁 检查输出文件...');
const outputFile = path.join(__dirname, 'examples', 'test-export-new.docx');
if (fs.existsSync(outputFile)) {
  const stats = fs.statSync(outputFile);
  console.log(`   ✅ Word文档存在: ${outputFile}`);
  console.log(`   📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
  console.log(`   🕒 修改时间: ${stats.mtime.toLocaleString()}`);
} else {
  console.log(`   ❌ Word文档不存在: ${outputFile}`);
}

console.log('\n📊 测试结果总结:');
console.log(`   ✅ 通过: ${passedTests}/${totalTests}`);
console.log(`   📈 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 所有命令测试通过！README中的命令说明正确。');
  process.exit(0);
} else {
  console.log('\n⚠️ 部分命令测试失败，请检查README中的命令说明。');
  process.exit(1);
}

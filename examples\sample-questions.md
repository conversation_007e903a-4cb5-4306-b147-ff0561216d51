# Word 试题解析服务示例文档

本文档展示了如何在 Word 文档中编写试题，以便 WordParser 服务能够正确解析。

## 格式说明

1. 每道题目需要以【题型】标记开头，如【单选题】、【多选题】等
2. 每道题目以【题干】作为开始标记
3. 选项需要使用 A.、B.、C.、D.等格式
4. 答案需要以【答案】开头
5. 解析需要以【解析】开头
6. 【页码】和【答题区】必须为数字，【答题区】表示排版时需要留出的空白行数

## 示例题目

### 示例 1：单选题

【题型】单选题
【题干】下列关于 Node.js 的描述，错误的是：

A. Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行环境
B. Node.js 使用事件驱动、非阻塞 I/O 模型
C. Node.js 只能用于开发服务器端应用程序
D. Node.js 的包管理器是 npm

【答案】C
【解析】Node.js 不仅可以用于开发服务器端应用程序，还可以用于开发桌面应用程序（如使用 Electron 框架）、命令行工具等。

### 示例 2：多选题

【题型】多选题
【题干】以下哪些是 Midway.js 的特点？

A. 基于 TypeScript 开发
B. 支持依赖注入
C. 只能用于开发 RESTful API
D. 支持面向切面编程(AOP)

【答案】ABD
【解析】Midway.js 是基于 TypeScript 开发的 Node.js 框架，支持依赖注入和面向切面编程，不仅可以用于开发 RESTful API，还可以开发 GraphQL API、WebSocket 服务等。

### 示例 3：判断题

【题型】判断题
【题干】Mammoth 库可以将 Word 文档转换为 HTML 格式。

【答案】正确
【解析】Mammoth 是一个 Node.js 库，可以将.docx 文件转换为 HTML、Markdown 等格式，便于进一步处理和解析。

### 示例 4：填空题

【题型】填空题
【题干】在 Node.js 中，**\_\_\_** 模块用于处理文件系统操作，**\_\_\_** 模块用于处理路径。

【答案】fs, path
【解析】fs 模块提供了文件读写、创建删除等操作，path 模块提供了处理文件路径的工具函数。

### 示例 5：简答题

【题型】简答题
【题干】简述 Midway.js 中依赖注入的实现原理和优势。

【答案】Midway.js 的依赖注入基于 TypeScript 的装饰器实现，通过@Provide()和@Inject()等装饰器来标记和注入依赖。其优势包括：1) 降低组件之间的耦合度；2) 便于单元测试，可以轻松模拟依赖；3) 提高代码的可维护性和可读性；4) 支持生命周期管理。

## 表格示例

【题型】表格题
【题干】下表展示了不同 Node.js 框架的特点比较：

| 框架名称  | 基于 TypeScript | 依赖注入 | 中间件支持 |
| --------- | --------------- | -------- | ---------- |
| Express   | 否              | 否       | 是         |
| Koa       | 否              | 否       | 是         |
| Nest.js   | 是              | 是       | 是         |
| Midway.js | 是              | 是       | 是         |

【答案】如表所示，Midway.js 和 Nest.js 都支持 TypeScript 和依赖注入，而 Express 和 Koa 原生不支持。

## 注意事项

1. 实际 Word 文档中，可以包含图片、公式等复杂元素
2. WordParser 服务会自动提取和处理这些元素
3. 对于复杂布局，可能需要人工校对修正解析结果

import { Provide, Config, Init, Scope, ScopeEnum } from '@midwayjs/core';
import { InjectQueue, Processor, IProcessor } from '@midwayjs/bullmq';
import { Job, Queue, QueueEvents } from 'bullmq';
import { EventEmitter } from 'events';

/**
 * 文件解析任务接口
 */
export interface FileParseTask {
  id: string; // 任务唯一标识符
  filePath: string; // 文件路径
  fileType: string; // 文件类型
  priority?: number; // 优先级 (1-最高, 10-最低)
  metadata?: Record<string, any>; // 元数据
}

/**
 * 任务进度接口
 */
export interface TaskProgress {
  taskId: string; // 任务ID
  progress: number; // 进度百分比 (0-100)
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed'; // 任务状态
  result?: any; // 任务结果
  error?: string; // 错误信息
  attempts?: number; // 尝试次数
  processedAt?: Date; // 处理时间
  finishedAt?: Date; // 完成时间
}

/**
 * 文件解析队列服务
 */
@Provide()
@Scope(ScopeEnum.Singleton) // 确保服务是单例的
export class FileQueueService extends EventEmitter {
  // 队列名称
  private readonly QUEUE_NAME = 'file-parse-queue';

  // 注入BullMQ队列
  @InjectQueue('file-parse-queue')
  private fileQueue: Queue;

  // 队列事件监听器
  private queueEvents: QueueEvents;

  // 配置信息
  @Config('fileQueue')
  private fileQueueConfig;

  /**
   * 初始化队列服务
   */
  @Init()
  async init() {
    // 初始化队列事件监听
    this.queueEvents = new QueueEvents(this.QUEUE_NAME, {
      connection: this.fileQueueConfig?.redis, // Redis连接配置
    });

    // 监听任务进度事件
    this.queueEvents.on('progress', ({ jobId, data }) => {
      this.emit('progress', {
        taskId: jobId,
        progress: data,
        status: 'active',
      });
    });

    // 监听任务完成事件
    this.queueEvents.on('completed', ({ jobId, returnvalue }) => {
      this.emit('completed', {
        taskId: jobId,
        progress: 100,
        status: 'completed',
        result: returnvalue,
      });
    });

    // 监听任务失败事件
    this.queueEvents.on('failed', ({ jobId, failedReason }) => {
      this.emit('failed', {
        taskId: jobId,
        progress: 0,
        status: 'failed',
        error: failedReason,
      });
    });

    console.log('文件解析队列服务初始化完成');
  }

  /**
   * 添加文件解析任务
   * @param task 文件解析任务
   * @returns 任务ID
   */
  async addTask(task: FileParseTask): Promise<string> {
    // 设置默认优先级
    const priority = task.priority || 5;

    // 添加任务到队列
    const job = await this.fileQueue.add(task.id, task, {
      priority, // 设置优先级
      jobId: task.id, // 使用传入的ID作为任务ID
      removeOnComplete: false, // 不自动删除完成的任务
      removeOnFail: false, // 不自动删除失败的任务
      attempts: 3, // 最多重试3次
      backoff: {
        type: 'exponential', // 指数退避策略
        delay: 1000, // 初始延迟1秒
      },
    });

    console.log(
      `添加文件解析任务: ${task.id}, 文件: ${task.filePath}, 优先级: ${priority}`
    );
    return job.id;
  }

  /**
   * 获取任务进度
   * @param taskId 任务ID
   * @returns 任务进度信息
   */
  async getTaskProgress(taskId: string): Promise<TaskProgress | null> {
    const job = await this.fileQueue.getJob(taskId);
    if (!job) {
      return null;
    }

    const state = await job.getState();
    const progress = (await job.progress) || 0;

    return {
      taskId: job.id,
      progress: progress,
      status: state as any,
      result: job.returnvalue,
      error: job.failedReason,
      attempts: job.attemptsMade,
      processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
      finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
    };
  }

  /**
   * 获取队列统计信息
   * @returns 队列统计信息
   */
  async getQueueStats() {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.fileQueue.getWaitingCount(),
      this.fileQueue.getActiveCount(),
      this.fileQueue.getCompletedCount(),
      this.fileQueue.getFailedCount(),
      this.fileQueue.getDelayedCount(),
    ]);

    return {
      waiting,
      active,
      completed,
      failed,
      delayed,
      total: waiting + active + completed + failed + delayed,
    };
  }

  /**
   * 清理队列中的所有任务
   */
  async cleanQueue() {
    await this.fileQueue.obliterate({ force: true });
    console.log('队列已清空');
  }

  /**
   * 关闭队列服务
   */
  async close() {
    await this.queueEvents?.close();
    console.log('文件解析队列服务已关闭');
  }
}

/**
 * 文件解析处理器
 */
@Processor('file-parse-queue')
export class FileProcessor implements IProcessor {
  /**
   * 处理文件解析任务
   * @param job 任务数据
   * @returns 处理结果
   */
  async execute(data: FileParseTask, job?: Job): Promise<any> {
    console.log(`开始处理文件解析任务: ${data.id}, 文件: ${data.filePath}`);

    try {
      // 更新进度为10%
      if (job) {
        await job.updateProgress(10);
      }

      // 模拟文件解析过程
      await this.simulateFileProcessing(data, job);

      // 返回处理结果
      return {
        success: true,
        taskId: data.id,
        message: `文件 ${data.filePath} 解析成功`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error(`文件解析任务失败: ${data.id}`, error);
      throw new Error(`文件解析失败: ${error.message}`);
    }
  }

  /**
   * 模拟文件处理过程
   * @param task 文件解析任务
   * @param job 可选的任务对象
   */
  private async simulateFileProcessing(
    task: FileParseTask,
    job?: Job
  ): Promise<void> {
    const totalSteps = 5;

    for (let step = 1; step <= totalSteps; step++) {
      // 计算当前进度
      const progress = Math.floor(10 + (step / totalSteps) * 90);

      // 更新任务进度
      if (job) {
        await job.updateProgress(progress);
      }

      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 随机模拟失败情况 (仅用于测试重试机制)
      if (Math.random() < 0.1 && job && job.attemptsMade === 0) {
        throw new Error('随机模拟的处理错误');
      }
    }
  }
}

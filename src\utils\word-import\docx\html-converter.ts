/**
 * Word文档HTML转换工具
 * 提供将Word XML转换为HTML的功能
 */
import { JSDOM } from 'jsdom';
import { applyTextStyleFromWordRun } from '../text';
import { processTable } from '../table/docx-table';
import { processDrawing } from '../image/docx-image';
import { processMath } from '../formula/docx-formula';

/**
 * 将Word XML转换为HTML
 * @param documentDoc document.xml的DOM
 * @param styles 样式映射表
 * @param imageData 图片数据映射表

 * @returns HTML内容
 */
export async function convertToHtml(
  documentDoc: Document,
  styles: Map<string, any>,
  imageData: Map<string, string> = new Map()
): Promise<string> {
  // 创建HTML文档 - 只使用基本结构，不添加样式表
  const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
  const document = dom.window.document;
  const body = document.body;

  // 获取文档主体
  const docBody = documentDoc.getElementsByTagName('w:body')[0];
  if (!docBody) {
    return '<p>无法解析文档主体</p>';
  }

  // 按顺序处理文档中的所有元素
  const childNodes = docBody.childNodes;
  for (let i = 0; i < childNodes.length; i++) {
    const node = childNodes[i];

    if (node.nodeName === 'w:tbl') {
      // 处理表格
      const htmlTable = processTable(node as Element, document);
      body.appendChild(htmlTable);
    } else if (node.nodeName === 'w:p') {
      // 处理段落
      const p = node as Element;

      // 检查段落是否包含公式
      const oMathElements = p.getElementsByTagName('m:oMath');
      if (oMathElements.length > 0) {
        // 创建段落元素
        const htmlP = document.createElement('p');
        htmlP.style.margin = '0'; // 添加一些边距
        htmlP.style.lineHeight = '1.5'; // 设置行高
        htmlP.style.whiteSpace = 'pre-wrap'; // 保留空格和换行

        // 检查段落内容，判断是否是注释块
        const pText = p.textContent || '';
        if (
          pText.trim().startsWith('/*') ||
          pText.trim().startsWith('*') ||
          pText.includes('*/')
        ) {
          // 对于注释块，使用等宽字体以确保星号对齐
          htmlP.style.fontFamily = 'monospace';
        }

        // 处理段落中公式前的文本内容
        const runs = p.getElementsByTagName('w:r');
        let foundMath = false;

        for (let j = 0; j < runs.length; j++) {
          const r = runs[j];

          // 检查是否包含公式
          if (
            r.getElementsByTagName('m:oMath').length > 0 ||
            r.getElementsByTagName('m:oMathPara').length > 0 ||
            foundMath
          ) {
            foundMath = true;
            continue; // 跳过公式及其后的内容
          }

          // 检查是否包含换行符
          const brElements = r.getElementsByTagName('w:br');
          if (brElements.length > 0) {
            // 添加换行符
            htmlP.appendChild(document.createElement('br'));
            continue;
          }

          // 检查是否包含制表符
          const tabElements = r.getElementsByTagName('w:tab');
          if (tabElements.length > 0) {
            // 添加制表符（使用空格模拟）
            const tabSpan = document.createElement('span');
            tabSpan.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;'; // 使用4个空格模拟制表符
            tabSpan.style.whiteSpace = 'pre';
            htmlP.appendChild(tabSpan);
            continue;
          }

          // 获取文本内容
          const textElements = r.getElementsByTagName('w:t');
          let text = '';

          for (let k = 0; k < textElements.length; k++) {
            // 直接获取文本内容
            text += textElements[k].textContent;
          }

          if (text) {
            // 创建span元素
            const span = document.createElement('span');
            span.textContent = text;

            // 设置white-space属性以保留空格
            span.style.whiteSpace = 'pre-wrap';

            // 检查是否是注释块（以 /* 开头或包含 * 的行）
            if (
              text.trim().startsWith('/*') ||
              text.trim().startsWith('*') ||
              text.includes('*/')
            ) {
              // 对于注释块，使用等宽字体以确保星号对齐
              span.style.fontFamily = 'monospace';
            }

            // 获取运行样式
            const rPrElements = r.getElementsByTagName('w:rPr');

            if (rPrElements.length > 0) {
              const rPr = rPrElements[0];
              // 应用文本样式
              applyTextStyleFromWordRun(span, rPr);
            }

            htmlP.appendChild(span);
          }
        }

        // 添加公式
        const mathElement = await processMath(oMathElements[0], document);
        htmlP.appendChild(mathElement);

        body.appendChild(htmlP);
        continue; // 跳过普通段落处理
      }

      const htmlP = document.createElement('p');
      htmlP.style.margin = '0'; // 添加一些边距
      htmlP.style.lineHeight = '1.5'; // 设置行高
      htmlP.style.whiteSpace = 'pre-wrap'; // 保留空格和换行

      // 检查段落内容，判断是否是注释块
      const pText = p.textContent || '';
      if (
        pText.trim().startsWith('/*') ||
        pText.trim().startsWith('*') ||
        pText.includes('*/')
      ) {
        // 对于注释块，使用等宽字体以确保星号对齐
        htmlP.style.fontFamily = 'monospace';
      }

      // 获取段落样式
      const pPrElements = p.getElementsByTagName('w:pPr');
      if (pPrElements.length > 0) {
        const pPr = pPrElements[0];
        const pStyleElements = pPr.getElementsByTagName('w:pStyle');

        if (pStyleElements.length > 0) {
          const styleId = pStyleElements[0].getAttribute('w:val');
          const style = styles.get(styleId);

          if (style) {
            // 应用段落样式
            if (style.fontSize) {
              htmlP.style.fontSize = `${style.fontSize}pt`;
            }
          }
        }

        // 获取段落对齐方式
        const jcElements = pPr.getElementsByTagName('w:jc');
        if (jcElements.length > 0) {
          const alignment = jcElements[0].getAttribute('w:val');

          switch (alignment) {
            case 'center':
              htmlP.style.textAlign = 'center';
              break;
            case 'right':
              htmlP.style.textAlign = 'right';
              break;
            case 'justify':
              htmlP.style.textAlign = 'justify';
              break;
            default:
              htmlP.style.textAlign = 'left';
          }
        }
      }

      // 获取所有文本运行
      const runs = p.getElementsByTagName('w:r');

      for (let j = 0; j < runs.length; j++) {
        const r = runs[j];

        // 检查是否包含图片
        const drawingElements = r.getElementsByTagName('w:drawing');
        if (drawingElements.length > 0) {
          const drawing = drawingElements[0];
          const img = processDrawing(drawing, document, imageData);
          if (img) {
            htmlP.appendChild(img);
            continue;
          }
        }

        // 检查是否包含换行符
        const brElements = r.getElementsByTagName('w:br');
        if (brElements.length > 0) {
          // 添加换行符
          htmlP.appendChild(document.createElement('br'));
          continue;
        }

        // 检查是否包含制表符
        const tabElements = r.getElementsByTagName('w:tab');
        if (tabElements.length > 0) {
          // 添加制表符（使用空格模拟）
          const tabSpan = document.createElement('span');
          tabSpan.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;'; // 使用4个空格模拟制表符
          tabSpan.style.whiteSpace = 'pre';
          htmlP.appendChild(tabSpan);
          continue;
        }

        // 获取文本内容
        const textElements = r.getElementsByTagName('w:t');
        let text = '';

        for (let k = 0; k < textElements.length; k++) {
          // 直接获取文本内容
          text += textElements[k].textContent;
        }

        if (text) {
          // 创建span元素
          const span = document.createElement('span');
          span.textContent = text;

          // 设置white-space属性以保留空格
          span.style.whiteSpace = 'pre-wrap';

          // 检查是否是注释块（以 /* 开头或包含 * 的行）
          if (
            text.trim().startsWith('/*') ||
            text.trim().startsWith('*') ||
            text.includes('*/')
          ) {
            // 对于注释块，使用等宽字体以确保星号对齐
            span.style.fontFamily = 'monospace';
          }

          // 获取运行样式
          const rPrElements = r.getElementsByTagName('w:rPr');

          if (rPrElements.length > 0) {
            const rPr = rPrElements[0];

            // 应用文本样式
            applyTextStyleFromWordRun(span, rPr);
          }

          htmlP.appendChild(span);
        }
      }

      body.appendChild(htmlP);
    }
  }

  // 最后一步：确保所有样式都是行内样式，不使用CSS类
  const allElements = document.querySelectorAll('*');
  allElements.forEach((el: Element) => {
    // 如果元素有class属性，将其样式转换为行内样式
    if (el.className) {
      // 移除class属性，我们只使用行内样式
      el.removeAttribute('class');
    }
  });

  // 返回HTML内容
  return body.innerHTML;
}

/**
 * 生成公式样式标签
 * @returns 公式样式CSS标签
 */
export function generateFormulaStyles(): string {
  return `
<style>
  .math-formula {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin: 0 3px;
  }
  .math-formula img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
    margin: 0;
    padding: 0;
  }
  p .math-formula {
    line-height: normal;
  }
  .math-text-fallback {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    display: none;
  }
  /* 确保公式与文本对齐 */
  p {
    line-height: 1.5;
  }
  /* 确保公式在表格中正确显示 */
  td .math-formula {
    margin: 0 2px;
  }
  /* 确保公式在标题中正确显示 */
  h1 .math-formula, h2 .math-formula, h3 .math-formula,
  h4 .math-formula, h5 .math-formula, h6 .math-formula {
    margin: 0 2px;
  }
</style>`;
}

/**
 * 添加公式样式到HTML
 * @param html HTML内容
 * @returns 添加了公式样式的HTML
 */
export function addFormulaStyles(html: string): string {
  if (html.includes('math-formula')) {
    const styleTag = generateFormulaStyles();

    // 在HTML的头部添加样式
    html = html.replace(
      '<head> </head>',
      `<head>
${styleTag}
</head>`
    );

    // 如果没有替换成功，则添加到body前
    if (!html.includes(styleTag)) {
      html = html.replace(
        '<body>',
        `<head>
${styleTag}
</head><body>`
      );
    }
  }

  return html;
}

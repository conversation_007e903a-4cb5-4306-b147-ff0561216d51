import {
  Paragraph,
  Run,
  ImageRun,
  Table,
  TableRow,
  TableCell,
  HeadingLevel,
} from 'docx';
import { StyleParser } from './style-parser';
import { TextProcessor } from './text-processor';
import { ImageProcessor } from './image-processor';
import sharp = require('sharp');

export class HtmlParser {
  private readonly NODE_TYPES = {
    ELEMENT_NODE: 1,
    TEXT_NODE: 3,
  };

  /**
   * 解析HTML节点
   */
  async parseHtmlNodes(
    nodeList: NodeList,
    margins?: { left?: number; right?: number }
  ): Promise<(Paragraph | Table)[]> {
    const result: (Paragraph | Table)[] = [];
    let currentParagraphRuns: Run[] = [];
    let currentParagraphAlignment: any = 'left';

    // 计算页面可用宽度
    const pageWidth = this.calculatePageWidth(margins || {});

    // Helper function to finalize the current paragraph
    const finalizeCurrentParagraph = () => {
      if (currentParagraphRuns.length > 0) {
        const paragraph = new Paragraph({
          children: currentParagraphRuns,
          alignment: currentParagraphAlignment,
          spacing: { line: 360 },
        });
        result.push(paragraph);
        currentParagraphRuns = [];
        currentParagraphAlignment = 'left';
      }
    };

    for (const node of Array.from(nodeList)) {
      if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;

        const parentElement = el.parentElement;
        const parentStyle = parentElement
          ? TextProcessor.extractTextStyle(parentElement)
          : {};
        const style = StyleParser.parseStyle(el);

        // 检查visibility样式，跳过隐藏的元素
        if (style.visibility === 'hidden') {
          continue;
        }

        // 按照新规则判断元素的内联/块级属性
        // 规则1: 自身通过display改变了类型
        // 规则2: 父容器定义flex，子节点全部按内联处理（只影响一层）

        // 首先确定元素的原始类型
        const isOriginallyBlockTag =
          el.tagName === 'P' ||
          el.tagName === 'TABLE' ||
          el.tagName.match(/^H[1-6]$/) ||
          el.tagName === 'UL' ||
          el.tagName === 'OL' ||
          el.tagName === 'DIV';

        const isOriginallyInlineTag =
          el.tagName === 'SPAN' ||
          el.tagName === 'A' ||
          el.tagName === 'IMG' ||
          el.tagName === 'B' ||
          el.tagName === 'STRONG' ||
          el.tagName === 'I' ||
          el.tagName === 'EM' ||
          el.tagName === 'U';

        // 规则1: 检查自身display属性是否改变了类型
        let isActuallyBlock = isOriginallyBlockTag;
        let isActuallyInline = isOriginallyInlineTag;

        if (style.display === 'inline' || style.display === 'inline-block') {
          isActuallyBlock = false;
          isActuallyInline = true;
        } else if (style.display === 'block') {
          isActuallyBlock = true;
          isActuallyInline = false;
        }

        // 规则2: 父容器flex影响（只影响一层，不递归）
        const isParentFlexContainer =
          parentStyle.display === 'flex' ||
          parentStyle.display === 'inline-flex';

        if (isParentFlexContainer) {
          // 父容器是flex时，所有子节点按内联处理
          isActuallyBlock = false;
          isActuallyInline = true;
        }

        // 标准块级元素：原本是块级且没有被改变类型的
        const isStandardBlockTag =
          isActuallyBlock &&
          (el.tagName === 'P' ||
            el.tagName === 'TABLE' ||
            el.tagName.match(/^H[1-6]$/) ||
            el.tagName === 'UL' ||
            el.tagName === 'OL');

        // 检查当前元素是否是flex容器
        const isCurrentElementFlexContainer =
          style.display === 'flex' || style.display === 'inline-flex';

        // 对于DIV元素，需要进一步判断如何处理
        const isDivBlock = el.tagName === 'DIV' && isActuallyBlock;

        // 检查DIV是否包含直接文本内容或主要是内联内容
        const containsDirectTextNode = Array.from(el.childNodes).some(
          child =>
            child.nodeType === this.NODE_TYPES.TEXT_NODE &&
            child.textContent?.trim()
        );

        const containsParagraphLikeContent =
          containsDirectTextNode ||
          Array.from(el.children).some(childEl => {
            const childStyle = StyleParser.parseStyle(childEl as Element);
            return (
              (childEl as Element).tagName === 'SPAN' ||
              (childEl as Element).tagName === 'B' ||
              (childEl as Element).tagName === 'STRONG' ||
              (childEl as Element).tagName === 'I' ||
              (childEl as Element).tagName === 'EM' ||
              (childEl as Element).tagName === 'U' ||
              (childEl as Element).tagName === 'A' ||
              (childEl as Element).tagName === 'IMG' ||
              childStyle.display === 'inline' ||
              childStyle.display === 'inline-block'
            );
          });

        // 对于包含文本内容的DIV，如果它实际上是块级的，应该作为段落处理
        const isTextContainingDivBlock =
          el.tagName === 'DIV' &&
          isActuallyBlock &&
          !isCurrentElementFlexContainer &&
          containsParagraphLikeContent;

        // 检查特定的DIV模式（如问题或选项容器）
        const isQuestionOrOptionContainer =
          el.tagName === 'DIV' &&
          (style.alignItems === 'center' ||
            parentStyle.alignItems === 'center') &&
          (style.whiteSpace === 'nowrap' ||
            parentStyle.whiteSpace === 'nowrap');

        if (isStandardBlockTag) {
          finalizeCurrentParagraph();

          if (el.tagName === 'P') {
            result.push(await this.parseParagraph(el));
          } else if (el.tagName === 'TABLE') {
            result.push(await this.parseTable(el));
          } else if (el.tagName.match(/^H[1-6]$/)) {
            result.push(await this.parseHeading(el));
          } else if (el.tagName === 'UL' || el.tagName === 'OL') {
            const numberingReference =
              el.tagName === 'UL' ? 'my-unordered-list' : 'my-ordered-list';
            const listParagraphs = await this.parseList(el, numberingReference);
            result.push(...listParagraphs);
          }
        } else if (isQuestionOrOptionContainer) {
          // 检查是否包含序号SPAN
          const hasNumberSpan = Array.from(el.children).some(
            child =>
              child.tagName === 'SPAN' &&
              child.textContent?.trim().match(/^\d+\.\s*$/)
          );

          // 对于序号容器，优先考虑保持内联布局
          if (hasNumberSpan && style.whiteSpace === 'nowrap') {
            // 特殊处理：序号与第一个块级元素在同一行，后续元素正常换行
            finalizeCurrentParagraph();

            const directChildren = Array.from(el.children);
            const spanChild = directChildren.find(
              child => child.tagName === 'SPAN'
            );
            const inlineBlockDiv = directChildren.find(child => {
              const childStyle = StyleParser.parseStyle(child as Element);
              return (
                child.tagName === 'DIV' && childStyle.display === 'inline-block'
              );
            });

            if (spanChild && inlineBlockDiv) {
              // 处理SPAN序号
              const spanRuns = await this.parseRuns(
                spanChild.childNodes,
                TextProcessor.extractTextStyle(
                  spanChild as Element,
                  parentStyle
                ),
                pageWidth
              );

              // 获取inline-block DIV的第一个块级子元素
              const divChildren = Array.from(inlineBlockDiv.children);
              const firstBlockChild = divChildren[0];

              if (
                firstBlockChild &&
                (firstBlockChild.tagName.match(/^H[1-6]$/) ||
                  firstBlockChild.tagName === 'P')
              ) {
                // 将SPAN和第一个块级元素合并为一个段落
                const firstBlockRuns = await this.parseRuns(
                  firstBlockChild.childNodes,
                  TextProcessor.extractTextStyle(
                    firstBlockChild as Element,
                    parentStyle
                  ),
                  pageWidth
                );

                const combinedRuns = [...spanRuns, ...firstBlockRuns];

                // 创建合并段落
                let paragraphAlignment: any = 'left';
                const firstBlockStyle = TextProcessor.extractTextStyle(
                  firstBlockChild as Element,
                  parentStyle
                );
                if (
                  firstBlockStyle.alignment &&
                  ['left', 'center', 'right', 'justify'].includes(
                    firstBlockStyle.alignment
                  )
                ) {
                  paragraphAlignment = firstBlockStyle.alignment;
                }

                // 根据第一个块级元素的类型设置段落属性
                const paragraphProps: any = {
                  children: combinedRuns,
                  alignment: paragraphAlignment,
                  spacing: { line: 360 },
                };

                if (firstBlockChild.tagName.match(/^H[1-6]$/)) {
                  const level = parseInt(
                    firstBlockChild.tagName.substring(1),
                    10
                  );
                  let heading: any = HeadingLevel.HEADING_1;
                  switch (level) {
                    case 1:
                      heading = HeadingLevel.HEADING_1;
                      break;
                    case 2:
                      heading = HeadingLevel.HEADING_2;
                      break;
                    case 3:
                      heading = HeadingLevel.HEADING_3;
                      break;
                    case 4:
                      heading = HeadingLevel.HEADING_4;
                      break;
                    case 5:
                      heading = HeadingLevel.HEADING_5;
                      break;
                    case 6:
                      heading = HeadingLevel.HEADING_6;
                      break;
                  }
                  paragraphProps.heading = heading;
                  paragraphProps.spacing = { after: 200, line: 360 };
                }

                result.push(new Paragraph(paragraphProps));

                // 处理剩余的块级元素
                const remainingChildren = divChildren.slice(1);
                for (const child of remainingChildren) {
                  if (child.tagName === 'P') {
                    const childParagraph = await this.parseParagraph(
                      child as Element
                    );
                    result.push(childParagraph);
                  } else if (child.tagName.match(/^H[1-6]$/)) {
                    const childHeading = await this.parseHeading(
                      child as Element
                    );
                    result.push(childHeading);
                  } else {
                    // 其他元素递归处理
                    const childElements = await this.parseHtmlNodes(
                      child.childNodes,
                      margins
                    );
                    result.push(...childElements);
                  }
                }
              } else {
                // 如果第一个子元素不是块级元素，回退到原有逻辑
                const children = await this.parseHtmlNodes(
                  el.childNodes,
                  margins
                );
                result.push(...children);
              }
            } else {
              // 如果结构不符合预期，回退到原有逻辑
              const children = await this.parseHtmlNodes(
                el.childNodes,
                margins
              );
              result.push(...children);
            }
          } else {
            // 对于其他问题容器，按原逻辑处理
            const blockElements = el.querySelectorAll(
              'h1, h2, h3, h4, h5, h6, p, table, ul, ol'
            );
            const containsBlockElements = blockElements.length > 0;

            if (containsBlockElements) {
              finalizeCurrentParagraph();
              const children = await this.parseHtmlNodes(
                el.childNodes,
                margins
              );
              result.push(...children);
            } else {
              finalizeCurrentParagraph();
              const runs = await this.parseRuns(
                el.childNodes,
                TextProcessor.extractTextStyle(el, parentStyle),
                pageWidth
              );
              if (runs.length > 0) {
                const combinedStyle = TextProcessor.extractTextStyle(
                  el,
                  parentStyle
                );
                let paragraphAlignment: any = 'left';
                if (
                  combinedStyle.alignment &&
                  ['left', 'center', 'right', 'justify'].includes(
                    combinedStyle.alignment
                  )
                ) {
                  paragraphAlignment = combinedStyle.alignment;
                }
                result.push(
                  new Paragraph({
                    children: runs,
                    alignment: paragraphAlignment,
                    spacing: { line: 360 },
                  })
                );
              }
            }
          }
        } else if (isTextContainingDivBlock) {
          // 对于包含文本内容的块级DIV，作为段落处理
          finalizeCurrentParagraph();
          const runs = await this.parseRuns(
            el.childNodes,
            TextProcessor.extractTextStyle(el, parentStyle),
            pageWidth
          );
          if (runs.length > 0) {
            const combinedStyle = TextProcessor.extractTextStyle(
              el,
              parentStyle
            );
            let paragraphAlignment: any = 'left';
            if (
              combinedStyle.alignment &&
              ['left', 'center', 'right', 'justify'].includes(
                combinedStyle.alignment
              )
            ) {
              paragraphAlignment = combinedStyle.alignment;
            }
            result.push(
              new Paragraph({
                children: runs,
                alignment: paragraphAlignment,
                spacing: { line: 360 },
              })
            );
          }
        } else if (isDivBlock) {
          // 对于其他块级DIV，递归处理子节点
          finalizeCurrentParagraph();
          const children = await this.parseHtmlNodes(el.childNodes, margins);
          result.push(...children);
        } else if (
          isActuallyInline ||
          isParentFlexContainer ||
          isCurrentElementFlexContainer
        ) {
          // 检查内联元素是否包含块级元素
          const containsBlockElements =
            el.querySelectorAll('h1, h2, h3, h4, h5, h6, p, table, ul, ol')
              .length > 0;

          if (containsBlockElements) {
            // 如果内联元素包含块级元素，需要递归处理而不是作为内联处理
            finalizeCurrentParagraph();
            const children = await this.parseHtmlNodes(el.childNodes, margins);
            result.push(...children);
          } else {
            // 正常的内联元素处理逻辑
            if (currentParagraphRuns.length === 0) {
              const inheritedStyle = TextProcessor.extractTextStyle(
                el,
                parentStyle
              );
              if (
                inheritedStyle.alignment &&
                ['left', 'center', 'right', 'justify'].includes(
                  inheritedStyle.alignment
                )
              ) {
                currentParagraphAlignment = inheritedStyle.alignment;
              }
            }

            const styleForRuns = TextProcessor.extractTextStyle(
              el,
              parentStyle
            );
            const childRuns = await this.parseRuns(
              el.childNodes,
              styleForRuns,
              pageWidth
            );

            currentParagraphRuns.push(...childRuns);
          }
        } else {
          finalizeCurrentParagraph();
          const children = await this.parseHtmlNodes(el.childNodes, margins);
          result.push(...children);
        }
      } else if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          currentParagraphRuns.push(new Run({ text }));
        }
      } else if (
        node.nodeType === (this.NODE_TYPES as any).DOCUMENT_FRAGMENT_NODE
      ) {
        const children = await this.parseHtmlNodes(node.childNodes, margins);
        result.push(...children);
      }
    }

    finalizeCurrentParagraph();

    return result;
  }

  /**
   * 解析段落
   */
  private async parseParagraph(el: Element): Promise<Paragraph> {
    const style = TextProcessor.extractTextStyle(el);
    const runs: Run[] = await this.parseRuns(el.childNodes, style);
    let alignment;
    if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }
    return new Paragraph({ children: runs, alignment, spacing: { line: 360 } });
  }

  /**
   * 解析标题
   */
  private async parseHeading(el: Element): Promise<Paragraph> {
    const parentElement = el.parentElement;
    const parentStyle = parentElement
      ? TextProcessor.extractTextStyle(parentElement, {})
      : {};
    const style = TextProcessor.extractTextStyle(el, parentStyle);
    const runs: Run[] = await this.parseRuns(el.childNodes, style);

    const level = parseInt(el.tagName.substring(1), 10);
    let heading:
      | 'Heading1'
      | 'Heading2'
      | 'Heading3'
      | 'Heading4'
      | 'Heading5'
      | 'Heading6'
      | 'Title' = HeadingLevel.HEADING_1;
    switch (level) {
      case 1:
        heading = HeadingLevel.HEADING_1;
        break;
      case 2:
        heading = HeadingLevel.HEADING_2;
        break;
      case 3:
        heading = HeadingLevel.HEADING_3;
        break;
      case 4:
        heading = HeadingLevel.HEADING_4;
        break;
      case 5:
        heading = HeadingLevel.HEADING_5;
        break;
      case 6:
        heading = HeadingLevel.HEADING_6;
        break;
    }

    let alignment: any;
    if (style.alignment) {
      if (['left', 'center', 'right', 'justify'].includes(style.alignment)) {
        alignment = style.alignment;
      }
    } else if (el.hasAttribute('align')) {
      const alignAttr = el.getAttribute('align')?.toLowerCase();
      if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
        alignment = alignAttr;
      }
    }

    const spacingAfter = 200;

    return new Paragraph({
      children: runs,
      heading,
      alignment,
      spacing: { after: spacingAfter, line: 360 },
    });
  }

  /**
   * 解析列表
   */
  private async parseList(
    el: Element,
    numberingReference: string
  ): Promise<Paragraph[]> {
    const result: Paragraph[] = [];
    for (const li of Array.from(el.querySelectorAll('li'))) {
      const runs = await this.parseRuns(li.childNodes);
      result.push(
        new Paragraph({
          children: runs,
          numbering: { reference: numberingReference, level: 0 },
          spacing: { line: 360 },
        })
      );
    }
    return result;
  }

  /**
   * 解析表格
   */
  private async parseTable(el: Element): Promise<Table> {
    const rows: TableRow[] = [];
    const trs = el.querySelectorAll('tr');
    for (const tr of trs) {
      const cells: TableCell[] = [];
      const tds = tr.querySelectorAll('td,th');
      for (const td of tds) {
        const cellParagraphs = await this.parseHtmlNodes(td.childNodes);
        const style = StyleParser.parseStyle(td);
        const width =
          style.width || parseInt(td.getAttribute('width') || '100', 10);

        let alignment;
        if (
          style.textAlign &&
          ['left', 'center', 'right', 'justify'].includes(style.textAlign)
        ) {
          alignment = style.textAlign;
        } else if (td.hasAttribute('align')) {
          const alignAttr = td.getAttribute('align')?.toLowerCase();
          if (['left', 'center', 'right', 'justify'].includes(alignAttr)) {
            alignment = alignAttr;
          }
        }

        let verticalAlign: 'center' | 'top' | 'bottom' | undefined;
        if (style.verticalAlign) {
          const v = style.verticalAlign.toLowerCase();
          if (v === 'middle' || v === 'center') verticalAlign = 'center';
          else if (v === 'top') verticalAlign = 'top';
          else if (v === 'bottom') verticalAlign = 'bottom';
        } else if (td.hasAttribute('valign')) {
          const valignAttr = td.getAttribute('valign')?.toLowerCase();
          if (valignAttr === 'middle' || valignAttr === 'center')
            verticalAlign = 'center';
          else if (valignAttr === 'top') verticalAlign = 'top';
          else if (valignAttr === 'bottom') verticalAlign = 'bottom';
        }

        const cellProps: any = {
          children: cellParagraphs.map(p => {
            if (p instanceof Paragraph) {
              const paragraphProps: any = {
                children: (p as any)._children || [],
                heading: (p as any).options?.heading,
                spacing: { line: 360 },
              };

              if (alignment) {
                paragraphProps.alignment = alignment;
              }

              if (verticalAlign) {
                paragraphProps.verticalAlign = verticalAlign;
              }

              return new Paragraph(paragraphProps);
            }
            return p;
          }),
          width: { size: width * 20, type: 'dxa' },
        };

        if (verticalAlign) {
          cellProps.verticalAlign = verticalAlign;
        }

        if (alignment) {
          cellProps.alignment = alignment;
        }

        cellProps.borders = {
          top: { style: 'single', size: 1, color: '000000' },
          bottom: { style: 'single', size: 1, color: '000000' },
          left: { style: 'single', size: 1, color: '000000' },
          right: { style: 'single', size: 1, color: '000000' },
        };

        cells.push(new TableCell(cellProps));
      }
      rows.push(new TableRow({ children: cells }));
    }
    return new Table({
      rows,
      width: {
        size: 100,
        type: 'pct',
      },
    });
  }

  /**
   * 解析运行块
   */
  private async parseRuns(
    nodeList: NodeList,
    parentStyle: any = {},
    pageWidth?: number
  ): Promise<Run[]> {
    const runs: Run[] = [];
    for (let i = 0; i < nodeList.length; i++) {
      const node = nodeList[i];
      if (node.nodeType === this.NODE_TYPES.TEXT_NODE) {
        const originalText = node.textContent || '';

        // 检查是否在下划线标签内（填空位置）
        const isInUnderline = this.isInUnderlineTag(node);

        // 标准化空格处理
        let text = this.normalizeSpaces(originalText, isInUnderline);

        // 检查是否在SPAN元素中
        const parentElement = node.parentElement;
        const isInSpan = parentElement && parentElement.tagName === 'SPAN';

        // 对于非SPAN元素，去除首尾空格
        if (!isInSpan) {
          text = text.trim();
        } else {
          // 对于SPAN元素，特殊处理序号空格
          const isNumberSpan = text.trim().match(/^\d+\.\s*$/);
          if (isNumberSpan) {
            // 序号SPAN：保留一个前导空格和后导空格
            text = ` ${text.trim()} `;
          }
        }

        if (text) {
          let currentColor = parentStyle.color;
          let currentEmphasis = parentStyle.emphasisMark;
          let currentNode = node.parentNode as Element;
          while (
            currentNode &&
            currentNode.nodeType === this.NODE_TYPES.ELEMENT_NODE
          ) {
            const elementStyle = StyleParser.parseStyle(currentNode);
            if (elementStyle.color !== undefined) {
              currentColor = elementStyle.color;
            }
            if (
              elementStyle.textEmphasis !== undefined ||
              elementStyle.textEmphasisPosition !== undefined
            ) {
              currentEmphasis = StyleParser.mapTextEmphasisToDocx(
                elementStyle.textEmphasis,
                elementStyle.textEmphasisPosition
              );
            }
            if (
              elementStyle.color !== undefined &&
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined)
            )
              break;
            if (
              elementStyle.color !== undefined &&
              parentStyle.color === undefined
            )
              break;
            if (
              (elementStyle.textEmphasis !== undefined ||
                elementStyle.textEmphasisPosition !== undefined) &&
              parentStyle.emphasisMark === undefined
            )
              break;

            currentNode = currentNode.parentNode as Element;
          }

          const run = new Run({
            text,
            bold: parentStyle.bold,
            italics: parentStyle.italics,
            underline: parentStyle.underline,
            color: currentColor !== undefined ? currentColor : '000000',
            emphasisMark: currentEmphasis,
          });

          runs.push(run);
        } else {
          const rawText = node.textContent ?? '';
          const pure = rawText.replace(/[\s\u3000]/g, '');
          if (rawText && pure.length === 0) {
            const parentTags = TextProcessor.getParentTagNamesUntilDiv(node);
            if (parentTags.length && parentTags.includes('U')) {
              runs.push(
                new Run({ text: rawText, underline: { type: 'single' } })
              );
              continue;
            }
          }
        }
      } else if (node.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
        const el = node as Element;

        // 检查绝对定位的元素，但保留其中的BR标签
        const style = StyleParser.parseStyle(el);
        if (style.position === 'absolute') {
          // 如果绝对定位元素包含BR标签，提取BR标签
          const brElements = el.querySelectorAll('br');
          if (brElements.length > 0) {
            // 为每个BR标签添加换行
            for (let i = 0; i < brElements.length; i++) {
              runs.push(new Run({ break: 1 }));
            }
          }
          continue;
        }

        // 跳过MSO注释和条件注释
        if (
          el.tagName === 'COMMENT' ||
          (el.textContent && el.textContent.includes('[if !supportLists]'))
        ) {
          continue;
        }

        // 处理换行标签
        if (el.tagName === 'BR') {
          runs.push(new Run({ break: 1 }));
          continue;
        }

        if (el.tagName === 'IMG') {
          const style = StyleParser.parseStyle(el);
          const src = el.getAttribute('src');
          if (src && src.startsWith('data:image/')) {
            if (src.startsWith('data:image/svg+xml')) {
              let width =
                style.width || parseInt(el.getAttribute('width') || '0', 10);
              let height =
                style.height || parseInt(el.getAttribute('height') || '0', 10);
              if (!width || !height) {
                let svgText = '';
                if (/;base64,/.test(src)) {
                  const base64 = src.split(';base64,').pop()!;
                  svgText = Buffer.from(base64, 'base64').toString();
                } else {
                  svgText = decodeURIComponent(src.split(',').pop()!);
                }
                const size = ImageProcessor.getSvgIntrinsicSize(svgText);
                if (size) {
                  if (!width) width = size.width;
                  if (!height) height = size.height;
                }
              }
              if (!width) width = 120;
              if (!height) height = 40;

              if (pageWidth && width > pageWidth) {
                const ratio = pageWidth / width;
                width = Math.round(width * ratio);
                height = Math.round(height * ratio);
              }

              const buffer = await ImageProcessor.svgBase64ToPngBuffer(
                src,
                width,
                height
              );

              const imageRunProps: any = {
                data: buffer,
                transformation: { width, height },
                type: 'png',
                verticalAlign: 'middle',
              };

              const parentElement = el.parentElement;

              if (parentElement) {
                const parentStyle = StyleParser.parseStyle(parentElement);
                if (parentStyle.display === 'inline-block') {
                  if (parentStyle.margin) {
                    const margins = parentStyle.margin.split(' ');
                    if (margins.length === 4) {
                      imageRunProps.margins = {
                        top: parseInt(margins[0]),
                        right: parseInt(margins[1]),
                        bottom: parseInt(margins[2]),
                        left: parseInt(margins[3]),
                      };
                    }
                  }
                }
              }

              runs.push(new ImageRun(imageRunProps));
            } else {
              const base64Data = src.split(';base64,').pop();
              if (base64Data) {
                const buffer = Buffer.from(base64Data, 'base64');

                let width =
                  style.width || parseInt(el.getAttribute('width') || '0', 10);
                let height =
                  style.height ||
                  parseInt(el.getAttribute('height') || '0', 10);

                if (width <= 0 || height <= 0) {
                  try {
                    const metadata = await sharp(buffer).metadata();
                    width = metadata.width || width;
                    height = metadata.height || height;
                  } catch (e) {
                    if (width <= 0) width = 100;
                    if (height <= 0) height = 100;
                  }
                }

                if (pageWidth && width > pageWidth) {
                  const ratio = pageWidth / width;
                  width = Math.round(width * ratio);
                  height = Math.round(height * ratio);
                }

                const imageRunProps: any = {
                  data: buffer,
                  transformation: { width, height },
                  type: 'png',
                  verticalAlign: 'middle',
                };

                const parentElement = el.parentElement;

                if (parentElement) {
                  const parentStyle = StyleParser.parseStyle(parentElement);
                  if (parentStyle.display === 'inline-block') {
                    if (parentStyle.margin) {
                      const margins = parentStyle.margin.split(' ');
                      if (margins.length === 4) {
                        imageRunProps.margins = {
                          top: parseInt(margins[0]),
                          right: parseInt(margins[1]),
                          bottom: parseInt(margins[2]),
                          left: parseInt(margins[3]),
                        };
                      }
                    }
                  }
                }

                runs.push(new ImageRun(imageRunProps));
              }
            }
          }
        } else {
          const style = TextProcessor.extractTextStyle(el, parentStyle);
          const styleForRuns = { ...style, alignment: undefined };
          const childRuns = await this.parseRuns(
            el.childNodes,
            styleForRuns,
            pageWidth
          );

          // 对于某些元素，在前后添加换行以确保正确的布局
          const needsLineBreakBefore =
            el.tagName === 'DIV' &&
            el.previousElementSibling &&
            (el.previousElementSibling.tagName === 'P' ||
              el.previousElementSibling.tagName.match(/^H[1-6]$/));

          const needsLineBreakAfter =
            el.tagName === 'DIV' &&
            el.nextElementSibling &&
            (el.nextElementSibling.tagName === 'P' ||
              el.nextElementSibling.tagName.match(/^H[1-6]$/));

          if (needsLineBreakBefore && childRuns.length > 0) {
            runs.push(new Run({ break: 1 }));
          }

          runs.push(...childRuns);

          if (needsLineBreakAfter && childRuns.length > 0) {
            runs.push(new Run({ break: 1 }));
          }
        }
      }
    }
    return runs;
  }

  /**
   * 计算页面宽度
   */
  private calculatePageWidth(margins: {
    left?: number;
    right?: number;
  }): number {
    const DEFAULT_MARGINS = {
      left: 1440,
      right: 1440,
    };
    const DEFAULT_PAGE_WIDTH_CM = 21;
    const CM_TO_PX = 37.8;

    const leftMargin = margins.left ?? DEFAULT_MARGINS.left;
    const rightMargin = margins.right ?? DEFAULT_MARGINS.right;
    const marginCm = (leftMargin + rightMargin) / 567;
    const maxWidthCm = DEFAULT_PAGE_WIDTH_CM - marginCm;
    return maxWidthCm * CM_TO_PX;
  }

  /**
   * 标准化空格处理
   */
  private normalizeSpaces(text: string, isUnderlineContent = false): string {
    let result = text
      // 将HTML实体空格转换为普通空格
      .replace(/&nbsp;/g, ' ')
      // 将全角空格转换为普通空格
      .replace(/　/g, ' ');

    // 如果是下划线内容（填空），保持空格数量，不合并
    if (!isUnderlineContent) {
      // 将多个连续空格合并为单个空格
      result = result.replace(/\s+/g, ' ');
    }

    return result;
  }

  /**
   * 检查节点是否在下划线标签内（填空位置）
   */
  private isInUnderlineTag(node: Node): boolean {
    let currentNode = node.parentNode;
    while (currentNode && currentNode.nodeType === this.NODE_TYPES.ELEMENT_NODE) {
      const element = currentNode as Element;
      if (element.tagName === 'U') {
        return true;
      }
      currentNode = currentNode.parentNode;
    }
    return false;
  }
}

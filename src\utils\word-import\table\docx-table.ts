/**
 * Word文档表格处理工具
 * 提供处理Word文档中表格的功能
 */
import { applyTextStyleFromWordRun } from '../text';

/**
 * 处理Word文档中的表格
 * @param tbl 表格元素
 * @param document HTML文档
 * @returns HTML表格元素
 */
export function processTable(
  tbl: Element,
  document: Document
): HTMLTableElement {
  const htmlTable = document.createElement('table');

  // 设置表格样式
  htmlTable.style.borderCollapse = 'collapse';
  htmlTable.style.width = '100%';
  htmlTable.style.border = '1px solid #000';

  // 获取所有行
  const rows = tbl.getElementsByTagName('w:tr');

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const htmlRow = document.createElement('tr');

    // 获取所有单元格
    const cells = row.getElementsByTagName('w:tc');

    for (let j = 0; j < cells.length; j++) {
      const cell = cells[j];
      const htmlCell = document.createElement('td');

      // 设置单元格样式
      htmlCell.style.border = '1px solid #000';
      htmlCell.style.padding = '0 8px';

      // 获取单元格内容
      const paragraphs = cell.getElementsByTagName('w:p');

      for (let k = 0; k < paragraphs.length; k++) {
        const p = paragraphs[k];
        const htmlP = document.createElement('p');
        htmlP.style.margin = '0'; // 添加一些边距
        htmlP.style.lineHeight = '1.5'; // 设置行高
        htmlP.style.whiteSpace = 'pre-wrap'; // 保留空格和换行

        // 检查段落内容，判断是否是注释块
        const pText = p.textContent || '';
        if (
          pText.trim().startsWith('/*') ||
          pText.trim().startsWith('*') ||
          pText.includes('*/')
        ) {
          // 对于注释块，使用等宽字体以确保星号对齐
          htmlP.style.fontFamily = 'monospace';
        }

        // 获取所有文本运行
        const runs = p.getElementsByTagName('w:r');

        for (let l = 0; l < runs.length; l++) {
          const r = runs[l];

          // 检查是否包含换行符
          const brElements = r.getElementsByTagName('w:br');
          if (brElements.length > 0) {
            // 添加换行符
            htmlP.appendChild(document.createElement('br'));
            continue;
          }

          // 检查是否包含制表符
          const tabElements = r.getElementsByTagName('w:tab');
          if (tabElements.length > 0) {
            // 添加制表符（使用空格模拟）
            const tabSpan = document.createElement('span');
            tabSpan.innerHTML = '&nbsp;&nbsp;&nbsp;&nbsp;'; // 使用4个空格模拟制表符
            tabSpan.style.whiteSpace = 'pre';
            htmlP.appendChild(tabSpan);
            continue;
          }

          // 获取文本内容
          const textElements = r.getElementsByTagName('w:t');
          let text = '';

          for (let m = 0; m < textElements.length; m++) {
            // 直接获取文本内容
            text += textElements[m].textContent;
          }

          if (text) {
            // 创建span元素
            const span = document.createElement('span');
            span.textContent = text;

            // 设置white-space属性以保留空格
            span.style.whiteSpace = 'pre-wrap';

            // 检查是否是注释块（以 /* 开头或包含 * 的行）
            if (
              text.trim().startsWith('/*') ||
              text.trim().startsWith('*') ||
              text.includes('*/')
            ) {
              // 对于注释块，使用等宽字体以确保星号对齐
              span.style.fontFamily = 'monospace';
            }

            // 获取运行样式
            const rPrElements = r.getElementsByTagName('w:rPr');

            if (rPrElements.length > 0) {
              const rPr = rPrElements[0];

              // 应用样式
              applyTextStyleFromWordRun(span, rPr);
            }

            htmlP.appendChild(span);
          }
        }

        htmlCell.appendChild(htmlP);
      }

      htmlRow.appendChild(htmlCell);
    }

    htmlTable.appendChild(htmlRow);
  }

  return htmlTable;
}

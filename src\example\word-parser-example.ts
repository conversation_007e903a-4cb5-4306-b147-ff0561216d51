/**
 * Word试题解析服务使用示例
 */
import { WordParserService, ParseResult } from '../service/word-parser.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 生成HTML预览文件
 * @param result 解析结果
 * @param outputPath 输出路径
 */
function generateHtmlPreview(result: ParseResult, outputPath: string): void {
  // 读取HTML模板
  const templatePath = path.join(__dirname, '../../testFiles/html-template.html');
  let template: string;

  if (fs.existsSync(templatePath)) {
    template = fs.readFileSync(templatePath, 'utf8');
  } else {
    // 如果模板不存在，使用默认模板
    template = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word解析结果预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .question { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }
        .question-header { background: #f5f5f5; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
        .question-type { font-weight: bold; color: #2c3e50; }
        .question-content { margin: 10px 0; }
        .options { margin: 10px 0; }
        .option { margin: 5px 0; padding: 5px; background: #f9f9f9; border-radius: 3px; }
        .answer { margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 3px; }
        .explanation { margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 3px; }
        .metadata { font-size: 0.9em; color: #666; margin-top: 10px; }
        .raw-html { margin-top: 20px; padding: 20px; background: #f8f8f8; border-radius: 5px; border-left: 4px solid #007acc; }
        .raw-html h3 { margin-top: 0; color: #007acc; }
        .raw-html .description { color: #666; font-size: 0.9em; margin-bottom: 15px; line-height: 1.4; }
        .raw-content { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background: white; font-family: monospace; font-size: 0.9em; }
        .toggle-btn { background: #007acc; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-bottom: 10px; }
        .toggle-btn:hover { background: #005a9e; }
    </style>
</head>
<body>
    <h1>Word解析结果预览</h1>
    <div class="summary">
        <p><strong>解析状态:</strong> {{success}}</p>
        <p><strong>题目数量:</strong> {{questionCount}}</p>
        <p><strong>生成时间:</strong> {{timestamp}}</p>
    </div>
    {{content}}
    {{raw}}
</body>
</html>`;
  }

  // 生成题目HTML内容
  let questionsHtml = '';
  if (result.questions && result.questions.length > 0) {
    result.questions.forEach((question, index) => {
      questionsHtml += `
        <div class="question">
          <div class="question-header">
            <span class="question-type">题目 ${index + 1} - ${question.type || '未知题型'}</span>
          </div>
          <div class="question-content">
            <strong>题干:</strong> ${question.content || '无题干'}
          </div>`;

      if (question.options && question.options.length > 0) {
        questionsHtml += '<div class="options"><strong>选项:</strong>';
        question.options.forEach((option: any, optIndex: number) => {
          questionsHtml += `<div class="option">${String.fromCharCode(65 + optIndex)}. ${option}</div>`;
        });
        questionsHtml += '</div>';
      }

      if (question.answer) {
        questionsHtml += `<div class="answer"><strong>答案:</strong> ${question.answer}</div>`;
      }

      if (question.explanation) {
        questionsHtml += `<div class="explanation"><strong>解析:</strong> ${question.explanation}</div>`;
      }

      // 添加元数据
      const metadata = [];
      if (question.knowledgePoints && question.knowledgePoints.length > 0) {
        metadata.push(`知识点: ${question.knowledgePoints.join(', ')}`);
      }
      if (question.difficulty) {
        metadata.push(`难度: ${question.difficulty}`);
      }
      if (question.level) {
        metadata.push(`层次: ${question.level}`);
      }
      if (question.pageNumber) {
        metadata.push(`页码: ${question.pageNumber}`);
      }

      if (metadata.length > 0) {
        questionsHtml += `<div class="metadata">${metadata.join(' | ')}</div>`;
      }

      questionsHtml += '</div>';
    });
  } else {
    questionsHtml = '<p>未解析到任何题目</p>';
  }

  // 添加Word解析后的HTML内容
  let rawHtml = '';
  if (result.raw) {
    rawHtml = `
      <div class="raw-html">
        <h3>📄 Word文档解析详情</h3>
        <div class="description">
          <strong>技术说明：</strong>此部分显示Word文档转换为HTML后的技术内容，包含完整的样式和格式信息。
          <br>这些HTML代码是解析引擎从Word文档中提取的，用于后续的题目识别和内容分析。
          <br><em>普通用户可以忽略此部分，主要供开发者调试使用。</em>
        </div>
        <button class="toggle-btn" onclick="toggleRawContent()">显示/隐藏 HTML源码</button>
        <div class="raw-content" id="rawContent" style="display: none;">${result.raw}</div>
        <script>
          function toggleRawContent() {
            const content = document.getElementById('rawContent');
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
          }
        </script>
      </div>`;
  }

  // 替换模板变量
  const finalHtml = template
    .replace('{{success}}', result.success ? '成功' : '失败')
    .replace('{{questionCount}}', (result.questions?.length || 0).toString())
    .replace('{{timestamp}}', new Date().toLocaleString('zh-CN'))
    .replace('{{content}}', questionsHtml)
    .replace('{{raw}}', rawHtml);

  // 保存文件
  fs.writeFileSync(outputPath, finalHtml, 'utf8');
}

/**
 * 示例：如何使用WordParser服务解析Word文档
 * @param filePath Word文档路径
 */
async function parseWordExample(filePath: string) {
  // 创建WordParser服务实例
  const wordParserService = new WordParserService();

  console.log(`开始解析Word文档: ${filePath}`);

  try {
    // 调用解析方法
    const result = await wordParserService.parseWordFile(filePath);

    if (result.success) {
      console.log('解析成功!');
      console.log(`共解析出 ${result.questions.length} 道试题`);

      // 输出解析结果
      console.log('\n试题列表:');
      result.questions.forEach((question, index) => {
        console.log(`\n题目 ${index + 1}:`);
        console.log(`题型: ${question.type}`);
        console.log(`题干: ${question.content}`);

        if (question.options && question.options.length > 0) {
          console.log('选项:');
          question.options.forEach((option, optIndex) => {
            console.log(`  ${String.fromCharCode(65 + optIndex)}. ${option}`);
          });
        }

        if (question.answer) {
          console.log(`答案: ${question.answer}`);
        }

        if (question.explanation) {
          console.log(`解析: ${question.explanation}`);
        }

        if (question.images && question.images.length > 0) {
          console.log(`包含 ${question.images.length} 张图片`);
        }
      });

      // 将结果保存为JSON文件
      const outputPath = path.join(
        path.dirname(filePath),
        `${path.basename(filePath, '.docx')}_parsed.json`
      );
      fs.writeFileSync(
        outputPath,
        JSON.stringify(result.questions, null, 2),
        'utf8'
      );
      console.log(`\n解析结果已保存至: ${outputPath}`);

      // 生成HTML预览文件
      const htmlPreviewPath = path.join(
        path.dirname(filePath),
        `${path.basename(filePath, '.docx')}_preview.html`
      );
      generateHtmlPreview(result, htmlPreviewPath);
      console.log(`HTML预览文件已保存至: ${htmlPreviewPath}`);

      return result.questions;
    } else {
      console.error(`解析失败: ${result.message}`);
      return [];
    }
  } catch (error) {
    console.error(`发生错误: ${error.message}`);
    return [];
  }
}

// 使用示例
// 如果直接运行此文件，可以通过命令行参数指定Word文件路径
if (require.main === module) {
  const args = process.argv.slice(2);
  const filePath = args[0];

  if (!filePath) {
    console.error('请提供Word文件路径作为参数');
    process.exit(1);
  }

  parseWordExample(filePath)
    .then(() => process.exit(0))
    .catch(err => {
      console.error(err);
      process.exit(1);
    });
}

export { parseWordExample };

/**
 * Word试题解析服务使用示例
 */
import { WordParserService } from '../service/word-parser.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 示例：如何使用WordParser服务解析Word文档
 * @param filePath Word文档路径
 */
async function parseWordExample(filePath: string) {
  // 创建WordParser服务实例
  const wordParserService = new WordParserService();

  console.log(`开始解析Word文档: ${filePath}`);

  try {
    // 调用解析方法
    const result = await wordParserService.parseWordFile(filePath);

    if (result.success) {
      console.log('解析成功!');
      console.log(`共解析出 ${result.questions.length} 道试题`);

      // 输出解析结果
      console.log('\n试题列表:');
      result.questions.forEach((question, index) => {
        console.log(`\n题目 ${index + 1}:`);
        console.log(`题型: ${question.type}`);
        console.log(`题干: ${question.content}`);

        if (question.options && question.options.length > 0) {
          console.log('选项:');
          question.options.forEach((option, optIndex) => {
            console.log(`  ${String.fromCharCode(65 + optIndex)}. ${option}`);
          });
        }

        if (question.answer) {
          console.log(`答案: ${question.answer}`);
        }

        if (question.explanation) {
          console.log(`解析: ${question.explanation}`);
        }

        if (question.images && question.images.length > 0) {
          console.log(`包含 ${question.images.length} 张图片`);
        }
      });

      // 将结果保存为JSON文件
      const outputPath = path.join(
        path.dirname(filePath),
        `${path.basename(filePath, '.docx')}_parsed.json`
      );
      fs.writeFileSync(
        outputPath,
        JSON.stringify(result.questions, null, 2),
        'utf8'
      );
      console.log(`\n解析结果已保存至: ${outputPath}`);

      return result.questions;
    } else {
      console.error(`解析失败: ${result.message}`);
      return [];
    }
  } catch (error) {
    console.error(`发生错误: ${error.message}`);
    return [];
  }
}

// 使用示例
// 如果直接运行此文件，可以通过命令行参数指定Word文件路径
if (require.main === module) {
  const args = process.argv.slice(2);
  const filePath = args[0];

  if (!filePath) {
    console.error('请提供Word文件路径作为参数');
    process.exit(1);
  }

  parseWordExample(filePath)
    .then(() => process.exit(0))
    .catch(err => {
      console.error(err);
      process.exit(1);
    });
}

export { parseWordExample };

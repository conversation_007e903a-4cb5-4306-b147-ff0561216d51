const { WordParserService } = require('./dist/service/word-parser.service');

async function main() {
  try {
    const service = new WordParserService();
    const result = await service.parseWordFile(
      'E:\\git\\github\\homework-design\\question-import-word\\examples\\题库模板.docx'
    );

    console.log('解析结果:');
    console.log('成功:', result.success);
    console.log('题目数量:', result.questions.length);

    if (result.questions.length > 0) {
      console.log('\n第一个题目:');
      const firstQuestion = result.questions[0];
      console.log('类型:', firstQuestion.type);
      console.log('内容:', firstQuestion.content);
      console.log('选项:', firstQuestion.options);
    }

    // 将结果保存到文件
    const fs = require('fs');
    fs.writeFileSync('parse-result.json', JSON.stringify(result, null, 2));
    console.log('\n完整结果已保存到 parse-result.json');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

main();

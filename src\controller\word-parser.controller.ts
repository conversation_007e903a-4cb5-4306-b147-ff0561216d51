import { Controller, Inject, Post, Body, Files, Fields } from '@midwayjs/core';
import { WordParserService } from '../service/word-parser.service';
import * as fs from 'fs';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';

@Controller('/word')
export class WordParserController {
  @Inject()
  wordParserService: WordParserService;

  /**
   * 解析本地Word文件
   * @param filePath Word文件路径
   */
  @Post('/parse')
  async parseWord(@Body() body: { filePath: string }) {
    const { filePath } = body;
    // 验证文件路径
    if (!filePath) {
      return { success: false, message: '文件路径不能为空' };
    }

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return { success: false, message: '文件不存在' };
    }

    // 调用服务解析Word文档
    const result = await this.wordParserService.parseWordFile(filePath);
    return result;
  }

  /**
   * 上传并解析Word文件
   */
  @Post('/upload', { middleware: [UploadMiddleware] })
  async uploadAndParse(
    @Files() files: Array<UploadFileInfo>,
    @Fields() fields: Record<string, string>
  ) {
    try {
      if (!files.length) {
        return { success: false, message: '未上传文件' };
      }
      console.log('fields', fields);
      const file = files[0];

      // 调用服务解析Word文档
      const result = await this.wordParserService.parseWordFile(file.data);
      return result;
    } catch (error) {
      return {
        success: false,
        message: `解析失败: ${error.message}`,
      };
    }
  }
}

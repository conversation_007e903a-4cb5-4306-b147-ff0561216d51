/**
 * Word文档解析工具
 * 提供基本的Word文档解析功能
 */
import * as fs from 'fs';
import * as J<PERSON>Z<PERSON> from 'jszip';
import { DOMParser } from 'xmldom';

/**
 * 从Word文档中提取XML内容
 * @param filePath Word文档路径
 * @returns 解析结果，包含文档、样式和关系XML
 */
export async function extractDocxXml(filePath: string): Promise<{
  documentDoc: Document;
  stylesDoc: Document;
  relsDoc: Document | null;
  zip: JSZip;
}> {
  try {
    // 读取Word文档
    const content = await fs.promises.readFile(filePath);

    // 使用JSZip解压Word文档
    const zip = await JSZip.loadAsync(content);

    // 获取document.xml文件，这是Word文档的主要内容
    const documentXml = await zip.file('word/document.xml').async('text');

    // 获取styles.xml文件，包含样式信息
    const stylesXml = await zip.file('word/styles.xml').async('text');

    // 获取图片关系文件
    let relsXml = '';
    try {
      relsXml = await zip.file('word/_rels/document.xml.rels').async('text');
    } catch (e) {
      console.warn('无法获取图片关系文件:', e);
    }

    // 解析XML
    const parser = new DOMParser();
    const documentDoc = parser.parseFromString(documentXml, 'text/xml');
    const stylesDoc = parser.parseFromString(stylesXml, 'text/xml');
    
    // 解析关系XML
    let relsDoc = null;
    if (relsXml) {
      relsDoc = parser.parseFromString(relsXml, 'text/xml');
    }

    return {
      documentDoc,
      stylesDoc,
      relsDoc,
      zip
    };
  } catch (error) {
    console.error('提取Word文档XML失败:', error);
    throw error;
  }
}

/**
 * 从styles.xml中提取样式信息
 * @param stylesDoc styles.xml的DOM
 * @returns 样式映射表
 */
export function extractStyles(stylesDoc: Document): Map<string, any> {
  const styles = new Map();

  // 获取所有样式定义
  const styleElements = stylesDoc.getElementsByTagName('w:style');

  for (let i = 0; i < styleElements.length; i++) {
    const style = styleElements[i];
    const styleId = style.getAttribute('w:styleId');

    // 提取颜色信息
    const colorElements = style.getElementsByTagName('w:color');
    let color = null;

    if (colorElements.length > 0) {
      color = colorElements[0].getAttribute('w:val');
    }

    // 提取字体信息
    const fontElements = style.getElementsByTagName('w:rFonts');
    let font = null;

    if (fontElements.length > 0) {
      font =
        fontElements[0].getAttribute('w:ascii') ||
        fontElements[0].getAttribute('w:eastAsia');
    }

    // 提取字体大小
    const szElements = style.getElementsByTagName('w:sz');
    let fontSize = null;

    if (szElements.length > 0) {
      fontSize = szElements[0].getAttribute('w:val');
      // Word中字体大小是磅值的两倍
      if (fontSize) {
        fontSize = parseInt(fontSize) / 2;
      }
    }

    // 提取粗体信息
    const bElements = style.getElementsByTagName('w:b');
    const isBold = bElements.length > 0;

    // 提取斜体信息
    const iElements = style.getElementsByTagName('w:i');
    const isItalic = iElements.length > 0;

    // 保存样式信息
    styles.set(styleId, {
      color,
      font,
      fontSize,
      isBold,
      isItalic,
    });
  }

  return styles;
}

/**
 * 从关系文件中提取图片关系
 * @param relsDoc 关系文件的DOM
 * @returns 图片关系映射表
 */
export function extractImageRelations(relsDoc: Document): Map<string, string> {
  const relations = new Map<string, string>();

  // 获取所有关系
  const relationshipElements = relsDoc.getElementsByTagName('Relationship');

  for (let i = 0; i < relationshipElements.length; i++) {
    const relationship = relationshipElements[i];
    const id = relationship.getAttribute('Id');
    const target = relationship.getAttribute('Target');
    const type = relationship.getAttribute('Type');

    // 只处理图片关系
    if (type && type.includes('image') && id && target) {
      relations.set(id, target);
    }
  }

  return relations;
}

/**
 * 从ZIP中提取图片数据
 * @param zip JSZip对象
 * @param imageRelations 图片关系映射表
 * @returns 图片数据映射表
 */
export async function extractImageData(
  zip: JSZip,
  imageRelations: Map<string, string>
): Promise<Map<string, string>> {
  const imageData = new Map<string, string>();

  for (const [id, target] of imageRelations.entries()) {
    try {
      // 图片路径通常是 word/media/image1.png
      const imagePath = `word/${target.replace(/^\//, '')}`;
      const imageFile = zip.file(imagePath);

      if (imageFile) {
        // 读取图片数据
        const data = await imageFile.async('base64');

        // 确定图片类型
        let mimeType = 'image/png'; // 默认为PNG

        if (imagePath.endsWith('.jpg') || imagePath.endsWith('.jpeg')) {
          mimeType = 'image/jpeg';
        } else if (imagePath.endsWith('.gif')) {
          mimeType = 'image/gif';
        } else if (imagePath.endsWith('.bmp')) {
          mimeType = 'image/bmp';
        } else if (imagePath.endsWith('.emf') || imagePath.endsWith('.wmf')) {
          // 对于EMF和WMF格式，我们可能无法直接显示，可以使用占位符
          mimeType = 'image/png';
        }

        // 创建data URL
        const dataUrl = `data:${mimeType};base64,${data}`;

        // 保存图片数据
        imageData.set(id, dataUrl);
      }
    } catch (error) {
      console.error(`提取图片数据失败: ${error}`);
    }
  }

  return imageData;
}

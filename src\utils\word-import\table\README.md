# 表格处理工具 (table)

## 模块简介

`table` 模块专门用于处理Word文档中的表格，负责将Word表格结构转换为HTML表格，同时保留表格的样式和内容。

## 主要功能

- 解析Word文档中的表格结构
- 处理表格样式（边框、对齐方式等）
- 处理表格单元格内容（文本、格式等）
- 支持嵌套表格和复杂布局

## 文件结构

```
table/
├── docx-table.ts     # Word文档表格处理
└── README.md         # 本文档
```

## API说明

### 主要函数

#### `processTable(tbl: Element, document: Document): HTMLTableElement`

处理Word文档中的表格，返回HTML表格元素。

**参数:**
- `tbl`: 表格元素
- `document`: HTML文档

**返回值:**
- 返回HTML表格元素

## 表格处理流程

1. **创建表格元素**: 创建HTML表格元素并设置基本样式
2. **处理表格行**: 遍历表格中的所有行
3. **处理表格单元格**: 遍历每行中的所有单元格
4. **处理单元格内容**: 处理单元格中的段落和文本
5. **应用样式**: 应用文本样式和表格样式

## 支持的表格属性

- **边框**: 表格边框和单元格边框
- **对齐方式**: 表格对齐和单元格内容对齐
- **宽度**: 表格宽度和单元格宽度
- **内边距**: 单元格内边距

## 使用示例

```typescript
import { processTable } from '../utils/word-import/table/docx-table';
import { DOMParser } from 'xmldom';

// 解析XML
const parser = new DOMParser();
const xml = `<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <!-- 表格XML内容 -->
</w:tbl>`;
const doc = parser.parseFromString(xml, 'text/xml');
const table = doc.getElementsByTagName('w:tbl')[0];

// 创建HTML文档
const jsdom = require('jsdom');
const { JSDOM } = jsdom;
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
const document = dom.window.document;

// 处理表格
const htmlTable = processTable(table, document);

// 将表格添加到文档
document.body.appendChild(htmlTable);
console.log(document.body.innerHTML);
```

## 表格结构示例

Word文档中的表格结构通常如下：

```xml
<w:tbl>
  <w:tblPr>
    <!-- 表格属性 -->
  </w:tblPr>
  <w:tr>
    <w:tc>
      <w:tcPr>
        <!-- 单元格属性 -->
      </w:tcPr>
      <w:p>
        <!-- 段落内容 -->
      </w:p>
    </w:tc>
    <!-- 更多单元格 -->
  </w:tr>
  <!-- 更多行 -->
</w:tbl>
```

转换后的HTML表格结构：

```html
<table style="border-collapse: collapse; width: 100%; border: 1px solid #000;">
  <tr>
    <td style="border: 1px solid #000; padding: 0 8px;">
      <p>单元格内容</p>
    </td>
    <!-- 更多单元格 -->
  </tr>
  <!-- 更多行 -->
</table>
```

## 注意事项

1. 复杂的表格布局（如合并单元格）可能需要额外处理
2. 表格样式是通过内联样式实现的，不使用CSS类
3. 表格中的文本样式通过`applyTextStyleFromWordRun`函数应用

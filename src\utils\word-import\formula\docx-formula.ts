/**
 * Word文档公式处理工具
 * 提供处理Word文档中公式的功能
 */
import { processFormula } from './mathjax-formula';

/**
 * 处理Word文档中的公式
 * @param oMath 公式元素
 * @param document HTML文档
 * @returns HTML元素
 */
export async function processMath(
  oMath: Element,
  document: Document
): Promise<HTMLElement> {
  // 创建一个span来包含公式（使用行内元素而非块级元素）
  const mathSpan = document.createElement('span');
  mathSpan.className = 'math-formula';
  mathSpan.style.display = 'inline-block'; // 使用inline-block保持行内特性
  mathSpan.style.verticalAlign = 'middle'; // 垂直居中对齐
  mathSpan.style.margin = '0 3px'; // 减小边距，使公式与文本更加协调

  try {
    // 使用MathJax处理公式
    const { dataURL } = await processFormula(oMath);

    // 创建图片元素
    const imgElement = document.createElement('img');
    imgElement.src = dataURL;
    imgElement.alt = '数学公式';
    imgElement.className = 'math-formula-image';
    imgElement.style.display = 'inline-block';
    imgElement.style.verticalAlign = 'middle';
    imgElement.style.maxWidth = '100%';
    imgElement.style.height = 'auto';
    imgElement.style.margin = '0';
    imgElement.style.padding = '0';

    // 调整公式大小
    imgElement.style.transform = 'scale(0.9)'; // 缩小到90%
    imgElement.style.transformOrigin = 'center center';

    // 将图片添加到span中
    mathSpan.appendChild(imgElement);

    // 添加自定义数据属性，用于识别公式
    mathSpan.setAttribute('data-formula', 'true');
  } catch (error) {
    console.error('处理公式失败:', error);
    // 显示更友好的占位符
    const errorPlaceholder = document.createElement('span');
    errorPlaceholder.textContent = '[数学公式]';
    errorPlaceholder.style.fontFamily = 'sans-serif';
    errorPlaceholder.style.backgroundColor = '#f5f5f5';
    errorPlaceholder.style.color = '#666666';
    errorPlaceholder.style.padding = '2px 6px';
    errorPlaceholder.style.borderRadius = '3px';
    errorPlaceholder.style.fontSize = '12px';
    mathSpan.appendChild(errorPlaceholder);
  }

  return mathSpan;
}

/**
 * LaTeX渲染器模块
 * 提供将LaTeX公式转换为图片URL的功能
 */

/**
 * 计算公式复杂度，用于动态调整图片大小
 * @param formula LaTeX公式
 * @returns 复杂度分数
 */
export function calculateFormulaComplexity(formula: string): number {
  let complexity = 0;

  // 基础长度分数
  complexity += formula.length * 0.5;

  // 特殊命令分数
  const specialCommands = [
    '\\frac',
    '\\sqrt',
    '\\sum',
    '\\int',
    '\\prod',
    '\\lim',
    '\\infty',
    '\\partial',
    '\\nabla',
    '\\oint',
    '\\begin{array}',
    '\\begin{matrix}',
    '\\begin{pmatrix}',
    '\\begin{bmatrix}',
    '\\begin{vmatrix}',
    '\\begin{cases}',
    '\\begin{align}',
  ];

  specialCommands.forEach(cmd => {
    const count = (
      formula.match(new RegExp(cmd.replace(/\\/g, '\\\\'), 'g')) || []
    ).length;
    complexity += count * 10; // 每个特殊命令增加10分
  });

  // 嵌套结构分数
  const openBraces = (formula.match(/\{/g) || []).length;
  const closeBraces = (formula.match(/\}/g) || []).length;
  complexity += Math.min(openBraces, closeBraces) * 5; // 每对花括号增加5分

  // 上下标分数
  const superscripts = (formula.match(/\^/g) || []).length;
  const subscripts = (formula.match(/_/g) || []).length;
  complexity += (superscripts + subscripts) * 3; // 每个上下标增加3分

  return complexity;
}

/**
 * 将LaTeX公式转换为图片URL
 * @param formula LaTeX公式文本
 * @param dpi 图片DPI（默认300）
 * @param useLocalFallback 是否使用本地渲染作为备选方案
 * @returns 图片URL
 */
export function convertFormulaToImage(formula: string, dpi = 300): string {
  try {
    // 对公式进行编码
    const encodedFormula = encodeURIComponent(formula);

    // 计算公式复杂度，用于动态调整图片大小
    const complexity = calculateFormulaComplexity(formula);

    // 根据公式复杂度动态调整DPI
    let finalDpi = dpi;
    if (complexity > 50) {
      finalDpi = 300; // 复杂公式使用更高DPI
    } else if (complexity > 100) {
      finalDpi = 350; // 非常复杂的公式使用最高DPI
    }

    // 分析公式类型，动态调整渲染参数
    const hasComplexStructures =
      formula.includes('\\frac') ||
      formula.includes('\\sqrt') ||
      formula.includes('\\sum') ||
      formula.includes('\\int') ||
      formula.includes('\\pm') ||
      formula.includes('\\begin{');

    // 对于复杂公式，使用更高的DPI和更大的字体
    if (hasComplexStructures) {
      finalDpi = Math.max(finalDpi, 400); // 增加DPI以提高清晰度

      // 对于二次方程公式，使用特殊处理
      if (
        formula.includes('\\frac') &&
        formula.includes('\\sqrt') &&
        (formula.includes('b^2-4ac') || formula.includes('b^{2}-4ac'))
      ) {
        // 二次方程公式
        return `https://latex.codecogs.com/png.latex?\\dpi{${finalDpi}}\\bg_white\\huge ${encodedFormula}`;
      }

      // 对于分数结构，使用特殊处理
      if (formula.includes('\\frac')) {
        // 确保分数结构完整显示
        return `https://latex.codecogs.com/png.latex?\\dpi{${finalDpi}}\\bg_white\\huge ${encodedFormula}`;
      }

      // 对于根号结构，使用特殊处理
      if (formula.includes('\\sqrt')) {
        return `https://latex.codecogs.com/png.latex?\\dpi{${finalDpi}}\\bg_white\\huge ${encodedFormula}`;
      }

      return `https://latex.codecogs.com/png.latex?\\dpi{${finalDpi}}\\bg_white\\huge ${encodedFormula}`;
    }

    // 使用CodeCogs服务渲染公式
    return `https://latex.codecogs.com/png.latex?\\dpi{${finalDpi}}\\bg_white\\large ${encodedFormula}`;
  } catch (error) {
    console.error('转换公式到图片URL失败:', error);

    // 如果转换失败，使用Google Chart API作为备选方案
    try {
      return `https://chart.googleapis.com/chart?cht=tx&chl=${encodeURIComponent(
        formula
      )}&chs=300&chf=bg,s,FFFFFF`;
    } catch (secondError) {
      console.error('Google Chart API渲染失败:', secondError);

      // 如果在线服务都失败，返回一个基本的数据URL
      return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(
        `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="30">
          <text x="5" y="20" font-family="monospace">${formula}</text>
        </svg>`
      )}`;
    }
  }
}

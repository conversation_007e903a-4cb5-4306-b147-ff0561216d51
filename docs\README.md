# 📚 文档目录说明

本目录包含了Word试题解析服务的详细技术文档。

## 📋 文档列表

### 🔧 功能说明文档

#### 1. [HTML转Word功能说明.md](./HTML转Word功能说明.md)
- **内容**: HTML转Word导出功能的完整使用指南
- **状态**: ✅ 已更新
- **适用人群**: 开发者、集成人员
- **主要内容**:
  - API接口使用方法
  - 代码集成示例
  - 快速验证命令
  - 技术实现原理
  - 限制和注意事项

#### 2. [NLP服务集成说明.md](./NLP服务集成说明.md)
- **内容**: NLP智能分析功能的集成文档
- **状态**: ✅ 当前版本
- **适用人群**: 开发者、系统集成人员
- **主要内容**:
  - 题型自动判断
  - 知识点提取
  - 相似题检测
  - 配置说明
  - 使用示例

#### 3. [文件解析队列使用说明.md](./文件解析队列使用说明.md)
- **内容**: BullMQ队列系统的完整使用指南
- **状态**: ✅ 当前版本
- **适用人群**: 开发者、运维人员
- **主要内容**:
  - 队列配置
  - 任务管理
  - 重试机制
  - 监控和统计
  - 性能优化

### 📋 设计文档

#### 4. [Word试题导入工具开发设计文档.md](./Word试题导入工具开发设计文档.md)
- **内容**: 项目整体设计和架构文档
- **状态**: ✅ 已更新至V2.0
- **适用人群**: 项目经理、架构师、开发者
- **主要内容**:
  - 项目概述和目标
  - 技术选型
  - 系统架构
  - 实现状态
  - 风险评估

## 🔄 文档维护建议

### ✅ 保留的文档
所有4个文档都建议保留，因为它们各自服务于不同的目的：

1. **HTML转Word功能说明.md** - 核心功能文档，已更新命令格式
2. **NLP服务集成说明.md** - 重要的扩展功能文档
3. **文件解析队列使用说明.md** - 系统架构重要组件文档
4. **Word试题导入工具开发设计文档.md** - 项目整体设计文档，已更新状态

### 📝 已完成的更新

#### HTML转Word功能说明.md
- ✅ 修复了硬编码路径问题
- ✅ 统一了命令格式，与README保持一致
- ✅ 添加了npx方式的命令示例

#### Word试题导入工具开发设计文档.md
- ✅ 更新版本号至2.0
- ✅ 更新日期至2025-05-19
- ✅ 添加项目状态说明
- ✅ 更新实现进度表，反映当前完成状态

### 🎯 文档结构优化

```
docs/
├── README.md                           ← 📚 本文档（文档导航）
├── HTML转Word功能说明.md                ← 🔧 功能使用指南
├── NLP服务集成说明.md                   ← 🤖 AI功能集成
├── 文件解析队列使用说明.md               ← ⚡ 队列系统指南
└── Word试题导入工具开发设计文档.md       ← 📋 项目设计文档
```

## 🚀 快速导航

- **想了解如何使用HTML转Word功能？** → [HTML转Word功能说明.md](./HTML转Word功能说明.md)
- **想集成NLP智能分析功能？** → [NLP服务集成说明.md](./NLP服务集成说明.md)
- **想了解队列系统配置？** → [文件解析队列使用说明.md](./文件解析队列使用说明.md)
- **想了解项目整体设计？** → [Word试题导入工具开发设计文档.md](./Word试题导入工具开发设计文档.md)

## 📖 与主README的关系

- **主README**: 面向用户的快速上手指南
- **docs目录**: 面向开发者的详细技术文档

建议用户先阅读主README进行快速体验，然后根据需要查阅docs目录中的具体技术文档。

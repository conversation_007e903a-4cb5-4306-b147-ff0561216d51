/**
 * Word文档解析工具主入口
 * 提供Word文档解析的统一接口
 */
import { extractDocxXml, extractStyles, extractImageRelations, extractImageData } from './parser';
import { convertToHtml, addFormulaStyles } from './html-converter';

/**
 * 解析Word文档，提取颜色信息
 * @param filePath Word文档路径

 * @returns HTML内容，保留颜色信息
 */
export async function parseDocxWithColors(filePath: string): Promise<string> {
  try {
    // 提取Word文档的XML内容
    const { documentDoc, stylesDoc, relsDoc, zip } = await extractDocxXml(filePath);

    // 提取样式信息
    const styles = extractStyles(stylesDoc);

    // 提取图片关系和数据
    let imageData = new Map<string, string>();
    if (relsDoc) {
      const imageRelations = extractImageRelations(relsDoc);
      imageData = await extractImageData(zip, imageRelations);
    }

    // 将XML转换为HTML，保留颜色信息
    let html = await convertToHtml(documentDoc, styles, imageData);

    // 添加公式样式
    html = addFormulaStyles(html);

    return html;
  } catch (error) {
    console.error('解析Word文档失败:', error);
    throw error;
  }
}

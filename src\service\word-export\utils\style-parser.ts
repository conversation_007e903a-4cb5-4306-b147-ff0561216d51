export class StyleParser {
  /**
   * 解析元素的样式
   */
  static parseStyle(el: Element): {
    color?: string;
    width?: number;
    height?: number;
    textDecoration?: string;
    fontWeight?: string;
    fontStyle?: string;
    textAlign?: string;
    verticalAlign?: string;
    display?: string;
    margin?: string;
    alignItems?: string;
    justifyContent?: string;
    whiteSpace?: string;
    textEmphasis?: string;
    textEmphasisPosition?: string;
  } {
    const style = el.getAttribute('style') || '';
    const result: {
      color?: string;
      width?: number;
      height?: number;
      textDecoration?: string;
      fontWeight?: string;
      fontStyle?: string;
      textAlign?: string;
      verticalAlign?: string;
      display?: string;
      margin?: string;
      alignItems?: string;
      justifyContent?: string;
      whiteSpace?: string;
      textEmphasis?: string;
      textEmphasisPosition?: string;
    } = {};

    // 提取颜色
    const colorMatch = style.match(/color:\s*([^;]+)/);
    if (colorMatch) {
      result.color = this.rgbToHex(colorMatch[1].trim())?.toUpperCase();
    }

    // 提取文本装饰（下划线等）
    const textDecorationMatch = style.match(/text-decoration:\s*([^;]+)/);
    if (textDecorationMatch) {
      result.textDecoration = textDecorationMatch[1].trim();
    }

    // 提取宽度
    const widthMatch = style.match(/width:\s*(\d+)px/);
    if (widthMatch) {
      result.width = parseInt(widthMatch[1], 10);
    }

    // 提取高度
    const heightMatch = style.match(/height:\s*(\d+)px/);
    if (heightMatch) {
      result.height = parseInt(heightMatch[1], 10);
    }

    // 提取 font-weight
    const fontWeightMatch = style.match(/font-weight:\s*([^;]+)/);
    if (fontWeightMatch) {
      result.fontWeight = fontWeightMatch[1].trim();
    }

    // 提取 font-style
    const fontStyleMatch = style.match(/font-style:\s*([^;]+)/);
    if (fontStyleMatch) {
      result.fontStyle = fontStyleMatch[1].trim();
    }

    // 提取 text-align
    const textAlignMatch = style.match(/text-align:\s*([^;]+)/);
    if (textAlignMatch) {
      result.textAlign = textAlignMatch[1].trim();
    }

    // 提取 vertical-align
    const verticalAlignMatch = style.match(/vertical-align:\s*([^;]+)/);
    if (verticalAlignMatch) {
      result.verticalAlign = verticalAlignMatch[1].trim();
    }

    // 提取 display
    const displayMatch = style.match(/display:\s*([^;]+)/);
    if (displayMatch) {
      result.display = displayMatch[1].trim();
    }

    // 提取 margin
    const marginMatch = style.match(/margin:\s*([^;]+)/);
    if (marginMatch) {
      result.margin = marginMatch[1].trim();
    }

    // 提取 align-items
    const alignItemsMatch = style.match(/align-items:\s*([^;]+)/);
    if (alignItemsMatch) {
      result.alignItems = alignItemsMatch[1].trim();
    }

    // 提取 justify-content
    const justifyContentMatch = style.match(/justify-content:\s*([^;]+)/);
    if (justifyContentMatch) {
      result.justifyContent = justifyContentMatch[1].trim();
    }

    // 提取 white-space
    const whiteSpaceMatch = style.match(/white-space:\s*([^;]+)/);
    if (whiteSpaceMatch) {
      result.whiteSpace = whiteSpaceMatch[1].trim();
    }

    // 提取 text-emphasis
    const textEmphasisMatch = style.match(/text-emphasis:\s*([^;]+)/);
    if (textEmphasisMatch) {
      result.textEmphasis = textEmphasisMatch[1].trim();
    }

    // 提取 text-emphasis-position
    const textEmphasisPositionMatch = style.match(
      /text-emphasis-position:\s*([^;]+)/
    );
    if (textEmphasisPositionMatch) {
      result.textEmphasisPosition = textEmphasisPositionMatch[1].trim();
    }

    return result;
  }

  /**
   * 将RGB颜色转换为十六进制
   */
  static rgbToHex(rgb: string): string | undefined {
    // 如果已经是十六进制颜色，直接返回
    if (/^#[0-9A-Fa-f]{6}$/.test(rgb.trim())) {
      return rgb.trim();
    }
    if (/^[0-9A-Fa-f]{6}$/.test(rgb.trim())) {
      return rgb.trim();
    }

    // 处理 rgb(r, g, b) 格式
    const rgbMatch = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (rgbMatch) {
      const r = parseInt(rgbMatch[1], 10);
      const g = parseInt(rgbMatch[2], 10);
      const b = parseInt(rgbMatch[3], 10);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理 rgba(r, g, b, a) 格式
    const rgbaMatch = rgb.match(
      /^rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\s*\)$/
    );
    if (rgbaMatch) {
      const r = parseInt(rgbaMatch[1], 10);
      const g = parseInt(rgbaMatch[2], 10);
      const b = parseInt(rgbaMatch[3], 10);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理 hsl(h, s%, l%) 格式
    const hslMatch = rgb.match(/^hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)$/);
    if (hslMatch) {
      const h = parseInt(hslMatch[1], 10);
      const s = parseInt(hslMatch[2], 10);
      const l = parseInt(hslMatch[3], 10);
      // 简单的 HSL 到 RGB 转换
      const c = ((1 - Math.abs((2 * l) / 100 - 1)) * s) / 100;
      const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
      const m = l / 100 - c / 2;
      let r = 0,
        g = 0,
        b = 0;
      if (h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      r = Math.round((r + m) * 255);
      g = Math.round((g + m) * 255);
      b = Math.round((b + m) * 255);
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // 处理颜色名称
    const colorNames: { [key: string]: string } = {
      black: '#000000',
      white: '#FFFFFF',
      red: '#FF0000',
      green: '#00FF00',
      blue: '#0000FF',
      yellow: '#FFFF00',
      purple: '#800080',
      gray: '#808080',
      grey: '#808080',
    };
    if (colorNames[rgb.toLowerCase()]) {
      return colorNames[rgb.toLowerCase()];
    }

    // 兜底：返回 undefined
    return undefined;
  }

  /**
   * 将CSS文本强调映射到docx EmphasisMark
   */
  static mapTextEmphasisToDocx(
    textEmphasis?: string,
    textEmphasisPosition?: string
  ):
    | {
        type: 'dot' | 'circle' | 'accent' | 'comma';
        position: 'above' | 'below';
      }
    | undefined {
    if (!textEmphasis) return undefined;

    let type: 'dot' | 'circle' | 'accent' | 'comma' = 'dot'; // Default type
    let position: 'above' | 'below' = 'above'; // Default position

    // Map emphasis type
    if (textEmphasis.includes('circle')) type = 'circle';
    else if (textEmphasis.includes('accent')) type = 'accent';
    else if (textEmphasis.includes('comma')) type = 'comma';
    // 'dot' is default

    // Map emphasis position (simplified: only supports 'under' for below)
    if (textEmphasisPosition && textEmphasisPosition.includes('under')) {
      position = 'below';
    }
    // 'above' is default

    return { type, position };
  }
}

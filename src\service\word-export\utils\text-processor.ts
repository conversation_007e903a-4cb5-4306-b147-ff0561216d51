import { StyleParser } from './style-parser';

export class TextProcessor {
  /**
   * 提取文本样式
   */
  static extractTextStyle(el: Element, parentStyle: any = {}): any {
    const style = StyleParser.parseStyle(el);
    // 解析 font-weight
    let fontWeight = style.fontWeight || el.getAttribute('font-weight');
    if (!fontWeight && el.hasAttribute('style')) {
      const fwMatch = el.getAttribute('style')?.match(/font-weight:\s*([^;]+)/);
      if (fwMatch) fontWeight = fwMatch[1];
    }
    // 解析 font-style
    let fontStyle = style.fontStyle || el.getAttribute('font-style');
    if (!fontStyle && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-style:\s*([^;]+)/);
      if (fsMatch) fontStyle = fsMatch[1];
    }
    // 解析 text-align
    let textAlign = style.textAlign;
    if (!textAlign && el.hasAttribute('style')) {
      const taMatch = el.getAttribute('style')?.match(/text-align:\s*([^;]+)/);
      if (taMatch) textAlign = taMatch[1];
    }
    return {
      bold:
        parentStyle.bold ||
        el.tagName === 'B' ||
        el.tagName === 'STRONG' ||
        fontWeight === 'bold' ||
        fontWeight === '700',
      italics:
        parentStyle.italics ||
        el.tagName === 'I' ||
        el.tagName === 'EM' ||
        fontStyle === 'italic',
      underline:
        parentStyle.underline ||
        el.tagName === 'U' ||
        (style.textDecoration && style.textDecoration.includes('underline')),
      color: style.color ? style.color.replace(/^#/, '') : parentStyle.color,
      alignment: textAlign || parentStyle.alignment,
      emphasisMark: StyleParser.mapTextEmphasisToDocx(
        style.textEmphasis,
        style.textEmphasisPosition
      ),
    };
  }

  /**
   * 向上递归查找父节点（第一个DIV时停止）
   */
  static getParentTagNamesUntilDiv(node: Node): string[] {
    const tagNames: string[] = [];
    let current = node.parentNode as Element | null;
    while (current && current.tagName !== 'DIV') {
      tagNames.push(current.tagName);
      current = current.parentNode as Element | null;
    }
    return tagNames;
  }
}

/**
 * Word文档图片处理工具
 * 提供处理Word文档中图片的功能
 */

/**
 * 处理Word文档中的图片
 * @param drawing 图片元素
 * @param document HTML文档
 * @param imageData 图片数据映射表
 * @returns HTML图片元素
 */
export function processDrawing(
  drawing: Element,
  document: Document,
  imageData: Map<string, string>
): HTMLImageElement | null {
  try {
    // 获取图片ID
    const blipElements = drawing.getElementsByTagName('a:blip');

    if (blipElements.length > 0) {
      const blip = blipElements[0];
      const embedId = blip.getAttribute('r:embed');

      if (embedId && imageData.has(embedId)) {
        // 创建图片元素
        const img = document.createElement('img');
        img.src = imageData.get(embedId) || '';

        // 获取图片尺寸
        const extentElements = drawing.getElementsByTagName('wp:extent');

        if (extentElements.length > 0) {
          const extent = extentElements[0];
          const cx = extent.getAttribute('cx');
          const cy = extent.getAttribute('cy');

          if (cx && cy) {
            // Word中的尺寸单位是EMU (English Metric Unit)
            // 1 inch = 914400 EMU, 1 inch = 96 pixels (默认)
            const widthInPixels = Math.round(parseInt(cx) / 9525);
            const heightInPixels = Math.round(parseInt(cy) / 9525);

            img.style.width = `${widthInPixels}px`;
            img.style.height = `${heightInPixels}px`;
          }
        }

        // 设置图片的垂直对齐方式
        img.style.verticalAlign = 'middle';
        img.style.display = 'inline-block';

        // 获取图片的对齐方式
        const docPrElements = drawing.getElementsByTagName('wp:docPr');
        if (docPrElements.length > 0) {
          const docPr = docPrElements[0];
          const nameAttr = docPr.getAttribute('name') || '';

          // 根据图片名称或其他属性判断对齐方式
          if (nameAttr.includes('右对齐') || nameAttr.includes('right')) {
            img.style.float = 'right';
            img.style.marginLeft = '10px';
          } else if (nameAttr.includes('左对齐') || nameAttr.includes('left')) {
            img.style.float = 'left';
            img.style.marginRight = '10px';
          }
        }

        // 检查是否有行内或浮动设置
        const positionElements = drawing.getElementsByTagName('wp:anchor');
        const inlineElements = drawing.getElementsByTagName('wp:inline');

        if (positionElements.length > 0) {
          // 浮动图片
          const positionH =
            positionElements[0].getElementsByTagName('wp:positionH');
          if (positionH.length > 0) {
            const alignH = positionH[0].getAttribute('relativeFrom');
            const alignVal = positionH[0].getElementsByTagName('wp:align');
            const posOffsetElements =
              positionH[0].getElementsByTagName('wp:posOffset');

            // 根据relativeFrom属性设置不同的对齐方式
            if (alignH) {
              // 设置基本定位参考样式
              switch (alignH) {
                case 'page':
                  // 相对于页面定位
                  img.style.position = 'relative';
                  break;
                case 'margin':
                  // 相对于页边距定位
                  img.style.position = 'relative';
                  break;
                case 'column':
                  // 相对于栏定位
                  img.style.position = 'relative';
                  break;
                case 'character':
                  // 相对于字符定位，更紧密地与文本集成
                  img.style.position = 'relative';
                  img.style.verticalAlign = 'text-bottom';
                  break;
                case 'leftMargin':
                  // 相对于左边距定位
                  img.style.position = 'relative';
                  img.style.float = 'left';
                  img.style.marginRight = '10px';
                  break;
                case 'rightMargin':
                  // 相对于右边距定位
                  img.style.position = 'relative';
                  img.style.float = 'right';
                  img.style.marginLeft = '10px';
                  break;
              }
            }

            // 处理对齐值
            if (alignVal.length > 0 && alignVal[0].textContent) {
              const align = alignVal[0].textContent.trim();
              if (align === 'right') {
                img.style.float = 'right';
                img.style.marginLeft = '10px';
              } else if (align === 'left') {
                img.style.float = 'left';
                img.style.marginRight = '10px';
              } else if (align === 'center') {
                // 居中对齐需要特殊处理
                img.style.display = 'block';
                img.style.float = 'none'; // 清除可能的浮动
                img.style.marginLeft = 'auto';
                img.style.marginRight = 'auto';
              }
            }

            // 处理精确偏移量（如果有）
            if (
              posOffsetElements.length > 0 &&
              posOffsetElements[0].textContent
            ) {
              const offset = parseInt(posOffsetElements[0].textContent.trim());
              if (!isNaN(offset)) {
                // 将EMU单位转换为像素 (1 inch = 914400 EMU, 1 inch = 96 pixels)
                const offsetPx = Math.round(offset / 9525);
                // 根据alignH的值决定应用偏移的方式
                if (alignH === 'leftMargin' || alignH === 'left') {
                  img.style.marginLeft = `${offsetPx}px`;
                } else if (alignH === 'rightMargin' || alignH === 'right') {
                  img.style.marginRight = `${offsetPx}px`;
                }
              }
            }
          }

          // 处理垂直定位
          const positionV =
            positionElements[0].getElementsByTagName('wp:positionV');
          if (positionV.length > 0) {
            const alignValV = positionV[0].getElementsByTagName('wp:align');

            if (alignValV.length > 0 && alignValV[0].textContent) {
              const alignV = alignValV[0].textContent.trim();
              if (alignV === 'top') {
                img.style.verticalAlign = 'top';
              } else if (alignV === 'center') {
                img.style.verticalAlign = 'middle';
              } else if (alignV === 'bottom') {
                img.style.verticalAlign = 'bottom';
              }
            }
          }
        } else if (inlineElements.length > 0) {
          // 行内图片
          img.style.verticalAlign = 'middle';
          img.style.display = 'inline-block';
        }

        return img;
      }
    }
  } catch (error) {
    console.error('处理图片失败:', error);
  }

  return null;
}

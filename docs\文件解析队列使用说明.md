# 文件解析队列使用说明

本文档提供了关于如何使用基于@midwayjs/bullmq@3实现的文件解析队列的完整说明，包括安装依赖、配置和使用示例。

## 功能特性

- 文件解析队列管理
- 失败任务自动重试（最多3次）
- 进度回调通知
- Redis连接池配置
- 任务状态持久化
- 优先级队列支持

## 安装依赖

首先，确保安装了必要的依赖：

```bash
npm install @midwayjs/bullmq@3 bullmq ioredis --save
```

## 配置说明

### 1. BullMQ组件配置

在`src/config/config.bullmq.ts`中配置BullMQ组件：

```typescript
import { MidwayConfig } from '@midwayjs/core';

export default {
  bullmq: {
    // 默认队列配置
    defaultQueueOptions: {
      // Redis连接配置
      connection: {
        host: 'localhost',
        port: 6379,
        db: 0,
        password: '',
        // 连接池配置
        maxRetriesPerRequest: 3,
        enableReadyCheck: false,
        connectTimeout: 10000,
      },
      // 前缀设置
      prefix: 'bullmq',
    },
    // 队列列表配置
    queues: [
      {
        name: 'file-parse-queue', // 队列名称
        // 队列特定配置，会覆盖默认配置
        options: {
          // 可以在这里覆盖默认队列选项
        },
        // Worker处理器配置
        worker: {
          // Worker处理器路径
          path: 'service/file-queue.service.ts',
          // 并发处理数
          concurrency: 2,
        },
      },
    ],
  },
} as MidwayConfig;
```

### 2. 文件队列配置

在`src/config/config.fileQueue.ts`中配置文件队列：

```typescript
import { MidwayConfig } from '@midwayjs/core';

export default {
  fileQueue: {
    // Redis连接池配置
    redis: {
      host: 'localhost',
      port: 6379,
      db: 0,
      password: '',
      // 连接池配置
      maxRetriesPerRequest: 3,
      enableReadyCheck: false,
      connectTimeout: 10000,
      // 连接池大小
      connectionPoolSize: 10,
    },
    
    // 队列处理配置
    concurrency: 2, // 并发处理数量
    
    // 任务持久化配置
    persistence: {
      // 任务保留时间（毫秒）
      keepCompleted: 24 * 60 * 60 * 1000, // 完成任务保留24小时
      keepFailed: 7 * 24 * 60 * 60 * 1000, // 失败任务保留7天
    },
    
    // 默认任务选项
    defaultJobOptions: {
      attempts: 3, // 最大重试次数
      backoff: {
        type: 'exponential', // 指数退避策略
        delay: 1000, // 初始延迟（毫秒）
      },
      timeout: 60000, // 任务超时时间（毫秒）
      removeOnComplete: false, // 不自动删除完成的任务
      removeOnFail: false, // 不自动删除失败的任务
    },
  },
} as MidwayConfig;
```

### 3. 在应用配置中引入BullMQ组件

在`src/configuration.ts`中引入BullMQ组件：

```typescript
import { Configuration } from '@midwayjs/core';
import * as bullmq from '@midwayjs/bullmq';

@Configuration({
  imports: [
    // 其他组件...
    bullmq,
  ],
  // 其他配置...
})
export class MainConfiguration {
  // ...
}
```

## 使用示例

### 1. 添加任务到队列

```typescript
import { Inject } from '@midwayjs/core';
import { FileQueueService, FileParseTask } from '../service/file-queue.service';

export class YourService {
  @Inject()
  fileQueueService: FileQueueService;
  
  async processFile(filePath: string) {
    // 创建任务
    const task: FileParseTask = {
      id: `task-${Date.now()}`,
      filePath: filePath,
      fileType: 'docx',
      priority: 5, // 1-最高优先级，10-最低优先级
      metadata: {
        userId: 'user123',
        timestamp: new Date().toISOString(),
      },
    };
    
    // 添加任务到队列
    const taskId = await this.fileQueueService.addTask(task);
    return taskId;
  }
}
```

### 2. 监听任务进度和结果

```typescript
import { Inject, Init } from '@midwayjs/core';
import { FileQueueService, TaskProgress } from '../service/file-queue.service';

export class YourService {
  @Inject()
  fileQueueService: FileQueueService;
  
  @Init()
  async init() {
    // 监听任务进度事件
    this.fileQueueService.on('progress', (progress: TaskProgress) => {
      console.log(`任务进度更新: ${progress.taskId}, 进度: ${progress.progress}%`);
      // 可以在这里更新数据库或通知前端
    });
    
    // 监听任务完成事件
    this.fileQueueService.on('completed', (result: TaskProgress) => {
      console.log(`任务完成: ${result.taskId}`, result.result);
      // 处理任务完成逻辑
    });
    
    // 监听任务失败事件
    this.fileQueueService.on('failed', (error: TaskProgress) => {
      console.log(`任务失败: ${error.taskId}, 错误: ${error.error}`);
      // 处理任务失败逻辑
    });
  }
}
```

### 3. 获取任务状态

```typescript
import { Inject } from '@midwayjs/core';
import { FileQueueService } from '../service/file-queue.service';

export class YourService {
  @Inject()
  fileQueueService: FileQueueService;
  
  async getTaskStatus(taskId: string) {
    const progress = await this.fileQueueService.getTaskProgress(taskId);
    return progress;
  }
  
  async getQueueStats() {
    const stats = await this.fileQueueService.getQueueStats();
    return stats;
  }
}
```

## API接口说明

### FileQueueService

#### 添加任务

```typescript
async addTask(task: FileParseTask): Promise<string>
```

- 参数：`task` - 文件解析任务对象
- 返回：任务ID

#### 获取任务进度

```typescript
async getTaskProgress(taskId: string): Promise<TaskProgress | null>
```

- 参数：`taskId` - 任务ID
- 返回：任务进度信息，如果任务不存在则返回null

#### 获取队列统计信息

```typescript
async getQueueStats(): Promise<object>
```

- 返回：队列统计信息，包括等待中、活动中、已完成、已失败和延迟的任务数量

#### 清理队列

```typescript
async cleanQueue(): Promise<void>
```

- 清空队列中的所有任务

#### 关闭队列服务

```typescript
async close(): Promise<void>
```

- 关闭队列服务，包括Worker和事件监听器

## 任务重试机制

文件解析队列默认配置了失败任务自动重试机制：

- 最大重试次数：3次
- 重试策略：指数退避（exponential backoff）
- 初始延迟：1000毫秒（1秒）

重试间隔会随着重试次数增加而指数增长：
- 第1次重试：1秒后
- 第2次重试：2秒后
- 第3次重试：4秒后

## 优先级队列

任务优先级范围为1-10，其中：
- 1：最高优先级
- 10：最低优先级

优先级较高的任务会优先被处理。

## 任务状态持久化

任务状态会持久化到Redis中，即使应用重启，任务状态也不会丢失。默认配置：

- 已完成任务保留时间：24小时
- 失败任务保留时间：7天

## 注意事项

1. 确保Redis服务器已正确配置并运行
2. 在生产环境中，建议配置Redis密码和适当的连接池大小
3. 对于大型文件处理，可能需要调整任务超时时间
4. 监控队列状态，避免任务堆积
/**
 * 表格处理模块
 * 提供表格处理相关功能
 */

/**
 * 表格样式信息
 */
export interface TableStyle {
  borderColor?: string;
  borderWidth?: string;
  backgroundColor?: string;
  textAlign?: string;
  verticalAlign?: string;
  width?: string;
  height?: string;
}

/**
 * 单元格样式信息
 */
export interface CellStyle extends TableStyle {
  colSpan?: number;
  rowSpan?: number;
  padding?: string;
}

/**
 * 从表格元素中提取表格样式
 * @param tbl 表格元素
 * @returns 表格样式
 */
export function extractTableStyle(tbl: Element): TableStyle {
  const style: TableStyle = {};

  try {
    // 提取表格属性
    const tblPr = tbl.getElementsByTagName('w:tblPr')[0];
    if (tblPr) {
      // 提取表格宽度
      const tblW = tblPr.getElementsByTagName('w:tblW')[0];
      if (tblW) {
        const width = tblW.getAttribute('w:w');
        const type = tblW.getAttribute('w:type');

        if (width && type === 'dxa') {
          // dxa单位是twip (1/20点)，转换为像素 (1点 = 1/72英寸，1英寸 = 96像素)
          const widthInPixels = Math.round(((parseInt(width) / 20) * 96) / 72);
          style.width = `${widthInPixels}px`;
        } else if (width && type === 'pct') {
          // pct单位是百分比的50倍
          const widthInPercent = Math.round(parseInt(width) / 50);
          style.width = `${widthInPercent}%`;
        }
      }

      // 提取表格边框
      const tblBorders = tblPr.getElementsByTagName('w:tblBorders')[0];
      if (tblBorders) {
        const top = tblBorders.getElementsByTagName('w:top')[0];
        if (top) {
          const color = top.getAttribute('w:color');
          const size = top.getAttribute('w:sz');

          if (color && color !== 'auto') {
            style.borderColor = `#${color}`;
          }

          if (size) {
            // 边框大小单位是1/8点，转换为像素
            const borderWidth = Math.max(1, Math.round(parseInt(size) / 8));
            style.borderWidth = `${borderWidth}px`;
          }
        }
      }

      // 提取表格背景色
      const shd = tblPr.getElementsByTagName('w:shd')[0];
      if (shd) {
        const fill = shd.getAttribute('w:fill');
        if (fill && fill !== 'auto') {
          style.backgroundColor = `#${fill}`;
        }
      }

      // 提取表格对齐方式
      const jc = tblPr.getElementsByTagName('w:jc')[0];
      if (jc) {
        const val = jc.getAttribute('w:val');
        if (val) {
          switch (val) {
            case 'left':
              style.textAlign = 'left';
              break;
            case 'center':
              style.textAlign = 'center';
              break;
            case 'right':
              style.textAlign = 'right';
              break;
          }
        }
      }
    }
  } catch (error) {
    console.error('提取表格样式失败:', error);
  }

  return style;
}

/**
 * 从单元格元素中提取单元格样式
 * @param tc 单元格元素
 * @returns 单元格样式
 */
export function extractCellStyle(tc: Element): CellStyle {
  const style: CellStyle = {};

  try {
    // 提取单元格属性
    const tcPr = tc.getElementsByTagName('w:tcPr')[0];
    if (tcPr) {
      // 提取单元格宽度
      const tcW = tcPr.getElementsByTagName('w:tcW')[0];
      if (tcW) {
        const width = tcW.getAttribute('w:w');
        const type = tcW.getAttribute('w:type');

        if (width && type === 'dxa') {
          // dxa单位是twip (1/20点)，转换为像素
          const widthInPixels = Math.round(((parseInt(width) / 20) * 96) / 72);
          style.width = `${widthInPixels}px`;
        }
      }

      // 提取单元格背景色
      const shd = tcPr.getElementsByTagName('w:shd')[0];
      if (shd) {
        const fill = shd.getAttribute('w:fill');
        if (fill && fill !== 'auto') {
          style.backgroundColor = `#${fill}`;
        }
      }

      // 提取单元格边框
      const tcBorders = tcPr.getElementsByTagName('w:tcBorders')[0];
      if (tcBorders) {
        const top = tcBorders.getElementsByTagName('w:top')[0];
        if (top) {
          const color = top.getAttribute('w:color');
          const size = top.getAttribute('w:sz');

          if (color && color !== 'auto') {
            style.borderColor = `#${color}`;
          }

          if (size) {
            // 边框大小单位是1/8点，转换为像素
            const borderWidth = Math.max(1, Math.round(parseInt(size) / 8));
            style.borderWidth = `${borderWidth}px`;
          }
        }
      }

      // 提取单元格垂直对齐方式
      const vAlign = tcPr.getElementsByTagName('w:vAlign')[0];
      if (vAlign) {
        const val = vAlign.getAttribute('w:val');
        if (val) {
          switch (val) {
            case 'top':
              style.verticalAlign = 'top';
              break;
            case 'center':
              style.verticalAlign = 'middle';
              break;
            case 'bottom':
              style.verticalAlign = 'bottom';
              break;
          }
        }
      }

      // 提取单元格合并信息
      const gridSpan = tcPr.getElementsByTagName('w:gridSpan')[0];
      if (gridSpan) {
        const val = gridSpan.getAttribute('w:val');
        if (val) {
          style.colSpan = parseInt(val);
        }
      }

      // 提取单元格垂直合并信息
      const vMerge = tcPr.getElementsByTagName('w:vMerge')[0];
      if (vMerge) {
        const val = vMerge.getAttribute('w:val');
        if (val === 'restart') {
          // 垂直合并的起始单元格
          // 需要计算合并的行数，这通常需要分析整个表格结构
          // 这里简化处理，默认合并2行
          style.rowSpan = 2;
        } else if (val === null || val === '') {
          // 被合并的单元格，在HTML中应该被忽略
          style.rowSpan = 0;
        }
      }

      // 提取单元格内边距
      const tcMar = tcPr.getElementsByTagName('w:tcMar')[0];
      if (tcMar) {
        const top = tcMar.getElementsByTagName('w:top')[0];
        const right = tcMar.getElementsByTagName('w:right')[0];
        const bottom = tcMar.getElementsByTagName('w:bottom')[0];
        const left = tcMar.getElementsByTagName('w:left')[0];

        let padding = '';

        if (top && top.getAttribute('w:w')) {
          const topPadding = Math.round(
            parseInt(top.getAttribute('w:w') || '0') / 20
          );
          padding += `${topPadding}px `;
        } else {
          padding += '0 ';
        }

        if (right && right.getAttribute('w:w')) {
          const rightPadding = Math.round(
            parseInt(right.getAttribute('w:w') || '0') / 20
          );
          padding += `${rightPadding}px `;
        } else {
          padding += '0 ';
        }

        if (bottom && bottom.getAttribute('w:w')) {
          const bottomPadding = Math.round(
            parseInt(bottom.getAttribute('w:w') || '0') / 20
          );
          padding += `${bottomPadding}px `;
        } else {
          padding += '0 ';
        }

        if (left && left.getAttribute('w:w')) {
          const leftPadding = Math.round(
            parseInt(left.getAttribute('w:w') || '0') / 20
          );
          padding += `${leftPadding}px`;
        } else {
          padding += '0';
        }

        style.padding = padding;
      }
    }

    // 提取单元格文本对齐方式（从段落属性中）
    const p = tc.getElementsByTagName('w:p')[0];
    if (p) {
      const pPr = p.getElementsByTagName('w:pPr')[0];
      if (pPr) {
        const jc = pPr.getElementsByTagName('w:jc')[0];
        if (jc) {
          const val = jc.getAttribute('w:val');
          if (val) {
            switch (val) {
              case 'left':
                style.textAlign = 'left';
                break;
              case 'center':
                style.textAlign = 'center';
                break;
              case 'right':
                style.textAlign = 'right';
                break;
              case 'both':
                style.textAlign = 'justify';
                break;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('提取单元格样式失败:', error);
  }

  return style;
}

/**
 * 应用表格样式到HTML表格元素
 * @param tableElement HTML表格元素
 * @param style 表格样式
 */
export function applyTableStyle(
  tableElement: HTMLTableElement,
  style: TableStyle
): void {
  if (style.borderColor) {
    tableElement.style.borderColor = style.borderColor;
  }

  if (style.borderWidth) {
    tableElement.style.borderWidth = style.borderWidth;
    tableElement.style.borderStyle = 'solid';
  }

  if (style.backgroundColor) {
    tableElement.style.backgroundColor = style.backgroundColor;
  }

  if (style.textAlign) {
    tableElement.style.textAlign = style.textAlign;
  }

  if (style.width) {
    tableElement.style.width = style.width;
  }

  // 设置表格的基本样式
  tableElement.style.borderCollapse = 'collapse';
  tableElement.style.marginBottom = '10px';
}

/**
 * 应用单元格样式到HTML单元格元素
 * @param cellElement HTML单元格元素
 * @param style 单元格样式
 */
export function applyCellStyle(
  cellElement: HTMLTableCellElement,
  style: CellStyle
): void {
  if (style.borderColor) {
    cellElement.style.borderColor = style.borderColor;
  }

  if (style.borderWidth) {
    cellElement.style.borderWidth = style.borderWidth;
    cellElement.style.borderStyle = 'solid';
  }

  if (style.backgroundColor) {
    cellElement.style.backgroundColor = style.backgroundColor;
  }

  if (style.textAlign) {
    cellElement.style.textAlign = style.textAlign;
  }

  if (style.verticalAlign) {
    cellElement.style.verticalAlign = style.verticalAlign;
  }

  if (style.width) {
    cellElement.style.width = style.width;
  }

  if (style.height) {
    cellElement.style.height = style.height;
  }

  if (style.colSpan && style.colSpan > 1) {
    cellElement.colSpan = style.colSpan;
  }

  if (style.rowSpan && style.rowSpan > 1) {
    cellElement.rowSpan = style.rowSpan;
  }

  if (style.padding) {
    cellElement.style.padding = style.padding;
  } else {
    // 默认内边距
    cellElement.style.padding = '2px 5px';
  }
}

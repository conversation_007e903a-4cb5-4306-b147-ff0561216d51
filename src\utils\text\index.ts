/**
 * 文本处理模块
 * 提供文本格式化相关功能
 */

/**
 * 文本样式信息
 */
export interface TextStyle {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strike?: boolean;
  color?: string;
  highlight?: string;
  fontSize?: string;
  fontFamily?: string;
  verticalAlign?: 'sub' | 'super' | 'baseline';
  emphasisMark?: string; // 着重号类型：'dot'（点）, 'comma'（逗号）, 'circle'（圆圈）等
}

/**
 * 从运行元素中提取文本样式
 * @param run 运行元素
 * @returns 文本样式
 */
export function extractTextStyle(run: Element): TextStyle {
  const style: TextStyle = {};

  try {
    // 提取运行属性
    const rPr = run.getElementsByTagName('w:rPr')[0];
    if (rPr) {
      // 检查是否加粗
      if (rPr.getElementsByTagName('w:b').length > 0) {
        style.bold = true;
      }

      // 检查是否斜体
      if (rPr.getElementsByTagName('w:i').length > 0) {
        style.italic = true;
      }

      // 检查是否下划线
      if (rPr.getElementsByTagName('w:u').length > 0) {
        style.underline = true;
      }

      // 检查是否删除线
      if (
        rPr.getElementsByTagName('w:strike').length > 0 ||
        rPr.getElementsByTagName('w:dstrike').length > 0
      ) {
        style.strike = true;
      }

      // 提取文本颜色
      const color = rPr.getElementsByTagName('w:color')[0];
      if (color) {
        const val = color.getAttribute('w:val');
        if (val && val !== 'auto') {
          style.color = `#${val}`;
        }
      }

      // 提取文本高亮颜色
      const highlight = rPr.getElementsByTagName('w:highlight')[0];
      if (highlight) {
        const val = highlight.getAttribute('w:val');
        if (val) {
          // 将Word高亮颜色转换为CSS颜色
          const highlightColorMap: { [key: string]: string } = {
            yellow: '#FFFF00',
            green: '#00FF00',
            cyan: '#00FFFF',
            magenta: '#FF00FF',
            blue: '#0000FF',
            red: '#FF0000',
            darkBlue: '#000080',
            darkCyan: '#008080',
            darkGreen: '#008000',
            darkMagenta: '#800080',
            darkRed: '#800000',
            darkYellow: '#808000',
            darkGray: '#808080',
            lightGray: '#C0C0C0',
            black: '#000000',
          };

          style.highlight = highlightColorMap[val] || val;
        }
      }

      // 提取字体大小
      const sz = rPr.getElementsByTagName('w:sz')[0];
      if (sz) {
        const val = sz.getAttribute('w:val');
        if (val) {
          // Word中的字体大小单位是半点，转换为点
          const fontSizeInPt = parseInt(val) / 2;
          style.fontSize = `${fontSizeInPt}pt`;
        }
      }

      // 提取字体
      const rFonts = rPr.getElementsByTagName('w:rFonts')[0];
      if (rFonts) {
        // 优先使用ASCII字体，然后是东亚字体，最后是高位ASCII字体
        const fontFamily =
          rFonts.getAttribute('w:ascii') ||
          rFonts.getAttribute('w:eastAsia') ||
          rFonts.getAttribute('w:hAnsi');

        if (fontFamily) {
          style.fontFamily = fontFamily;
        }
      }

      // 提取垂直对齐方式
      const vertAlign = rPr.getElementsByTagName('w:vertAlign')[0];
      if (vertAlign) {
        const val = vertAlign.getAttribute('w:val');
        if (val === 'subscript') {
          style.verticalAlign = 'sub';
        } else if (val === 'superscript') {
          style.verticalAlign = 'super';
        }
      }

      // 提取着重号
      const em = rPr.getElementsByTagName('w:em')[0];
      if (em) {
        const val = em.getAttribute('w:val');
        if (val && val !== 'none') {
          style.emphasisMark = val;
        }
      }
    }
  } catch (error) {
    console.error('提取文本样式失败:', error);
  }

  return style;
}

/**
 * 应用文本样式到HTML元素
 * @param element HTML元素
 * @param style 文本样式
 */
export function applyTextStyle(element: HTMLElement, style: TextStyle): void {
  if (style.bold) {
    element.style.fontWeight = 'bold';
  }

  if (style.italic) {
    element.style.fontStyle = 'italic';
  }

  if (style.underline) {
    element.style.textDecoration = 'underline';
  }

  if (style.strike) {
    // 如果已经有下划线，添加删除线；否则，只设置删除线
    if (element.style.textDecoration.includes('underline')) {
      element.style.textDecoration += ' line-through';
    } else {
      element.style.textDecoration = 'line-through';
    }
  }

  if (style.color) {
    element.style.color = style.color;
  }

  if (style.highlight) {
    element.style.backgroundColor = style.highlight;
  }

  if (style.fontSize) {
    element.style.fontSize = style.fontSize;
  }

  if (style.fontFamily) {
    element.style.fontFamily = style.fontFamily;
  }

  if (style.verticalAlign) {
    element.style.verticalAlign = style.verticalAlign;
  }

  // 处理着重号
  if (style.emphasisMark) {
    // 使用CSS text-emphasis属性添加着重号
    // 注意：text-emphasis是CSS3的属性，可能需要浏览器前缀
    element.style.textEmphasis = style.emphasisMark;
    // 为了兼容性，也可以使用伪元素和定位来模拟着重号
    if (style.emphasisMark === 'dot') {
      // 使用text-decoration-style: dotted作为备选方案
      element.style.textEmphasis = 'filled currentColor';
      element.style.textEmphasisPosition = 'under right';
      // 添加自定义属性，以便可以通过CSS选择器定位
      element.setAttribute('data-emphasis-mark', 'dot');
    }
  }
}

/**
 * 从Word文档的运行属性中应用文本样式
 * @param span HTML span元素
 * @param rPr 运行属性元素
 */
export function applyTextStyleFromWordRun(
  span: HTMLSpanElement,
  rPr: Element
): void {
  // 获取颜色
  const colorElements = rPr.getElementsByTagName('w:color');

  if (colorElements.length > 0) {
    const color = colorElements[0].getAttribute('w:val');

    if (color) {
      span.style.color = `#${color}`;
    }
  }

  // 获取字体
  const fontElements = rPr.getElementsByTagName('w:rFonts');

  if (fontElements.length > 0) {
    // 优先使用eastAsia字体，因为我们主要处理中文文档
    let font = fontElements[0].getAttribute('w:eastAsia');

    // 如果没有eastAsia字体，尝试使用ascii字体
    if (!font) {
      font = fontElements[0].getAttribute('w:ascii');
    }

    // 如果没有ascii字体，尝试使用hAnsi字体
    if (!font) {
      font = fontElements[0].getAttribute('w:hAnsi');
    }

    if (font) {
      span.style.fontFamily = font;
    }
  }

  // 获取字体大小
  const szElements = rPr.getElementsByTagName('w:sz');

  if (szElements.length > 0) {
    const fontSize = szElements[0].getAttribute('w:val');

    if (fontSize && fontSize !== '0') {
      // Word中字体大小是磅值的两倍
      span.style.fontSize = `${parseInt(fontSize) / 2}pt`;
    }
  }

  // 获取复合字体大小
  const szCsElements = rPr.getElementsByTagName('w:szCs');

  if (szCsElements.length > 0 && !span.style.fontSize) {
    const fontSizeCs = szCsElements[0].getAttribute('w:val');

    if (fontSizeCs && fontSizeCs !== '0') {
      // Word中字体大小是磅值的两倍
      span.style.fontSize = `${parseInt(fontSizeCs) / 2}pt`;
    }
  }

  // 获取粗体
  const bElements = rPr.getElementsByTagName('w:b');

  if (bElements.length > 0) {
    // 检查是否有val属性，如果val="0"则不加粗
    const bVal = bElements[0].getAttribute('w:val');
    if (bVal !== '0') {
      span.style.fontWeight = 'bold';
    }
  }

  // 检查是否有粗体值 (复合字体)
  const bValElements = rPr.getElementsByTagName('w:bCs');
  if (bValElements.length > 0) {
    // 检查是否有val属性，如果val="0"则不加粗
    const bCsVal = bValElements[0].getAttribute('w:val');
    if (bCsVal !== '0') {
      span.style.fontWeight = 'bold';
    }
  }

  // 获取斜体
  const iElements = rPr.getElementsByTagName('w:i');

  if (iElements.length > 0) {
    // 检查是否有val属性，如果val="0"则不应用斜体
    const iVal = iElements[0].getAttribute('w:val');
    if (iVal !== '0') {
      span.style.fontStyle = 'italic';
    }
  }

  // 获取下划线
  const uElements = rPr.getElementsByTagName('w:u');

  if (uElements.length > 0) {
    // 检查是否有val属性，如果val="0"或val="none"则不应用下划线
    const uVal = uElements[0].getAttribute('w:val');
    if (uVal !== '0' && uVal !== 'none') {
      span.style.textDecoration = 'underline';
    }
  }

  // 获取着重号
  const emElements = rPr.getElementsByTagName('w:em');
  if (emElements.length > 0) {
    // 检查着重号类型
    const emVal = emElements[0].getAttribute('w:val');
    if (emVal && emVal !== 'none') {
      // 使用CSS text-emphasis属性添加着重号
      span.style.textEmphasis = emVal;

      // 为了兼容性，也可以使用伪元素和定位来模拟着重号
      if (emVal === 'dot') {
        // 使用text-decoration-style: dotted作为备选方案
        span.style.textEmphasis = 'filled currentColor';
        span.style.textEmphasisPosition = 'under right';
        // 添加自定义属性，以便可以通过CSS选择器定位
        span.setAttribute('data-emphasis-mark', 'dot');
      }
    }
  }
}

/**
 * 创建带样式的文本元素
 * @param text 文本内容
 * @param style 文本样式
 * @param document HTML文档
 * @returns HTML元素
 */
export function createStyledTextElement(
  text: string,
  style: TextStyle,
  document: Document
): HTMLElement {
  const span = document.createElement('span');
  span.textContent = text;
  applyTextStyle(span, style);
  return span;
}

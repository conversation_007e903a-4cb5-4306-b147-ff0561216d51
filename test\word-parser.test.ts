import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework, Application } from '@midwayjs/koa';
import { join } from 'path';
import { WordParserService } from '../src/service/word-parser.service';
import * as fs from 'fs';
import * as path from 'path';

describe('测试 WordParser 服务', () => {
  let app: Application;
  let wordParserService: WordParserService;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>(join(__dirname, '../src'), {
      cleanLogsDir: true,
    });

    // 获取服务实例
    wordParserService = await app
      .getApplicationContext()
      .getAsync(WordParserService);
  }, 30000); // 30秒超时

  afterAll(async () => {
    if (app) {
      await close(app);
    }
  }, 10000); // 10秒超时

  it('应该能够检测非法文件路径', async () => {
    const result = await wordParserService.parseWordFile('不存在的文件.docx');
    expect(result.success).toBe(false);
    expect(result.message).toContain('文件不存在');
  });

  it('应该能够检测非docx格式文件', async () => {
    // 创建一个临时的非docx文件用于测试
    const tempFilePath = path.join(__dirname, 'temp.txt');
    fs.writeFileSync(tempFilePath, 'test content', 'utf8');

    try {
      const result = await wordParserService.parseWordFile(tempFilePath);
      expect(result.success).toBe(false);
      expect(result.message).toContain('仅支持.docx格式文件');
    } finally {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    }
  });

  // 注意：以下测试需要一个实际的docx文件才能运行
  // 如果有测试文件，可以取消注释并修改文件路径
  /*
  it('应该能够解析docx文件并提取试题', async () => {
    // 准备一个包含试题的测试docx文件
    const testFilePath = path.join(__dirname, 'test-files/sample-questions.docx');

    // 确保测试文件存在
    if (!fs.existsSync(testFilePath)) {
      console.warn(`测试文件不存在: ${testFilePath}，跳过测试`);
      return;
    }

    const result = await wordParserService.parseWordFile(testFilePath);

    // 验证解析结果
    expect(result.success).toBe(true);
    expect(result.questions.length).toBeGreaterThan(0);

    // 验证第一个题目的结构
    const firstQuestion = result.questions[0];
    expect(firstQuestion).toHaveProperty('type');
    expect(firstQuestion).toHaveProperty('content');
  });
  */

  it('应该能够正确识别题型', () => {
    // 使用私有方法测试题型识别
    // 注意：这里使用了TypeScript的类型断言来访问私有方法
    const mapQuestionType = (wordParserService as any).mapQuestionType.bind(
      wordParserService
    );

    expect(mapQuestionType('[单选题]')).toBe('单选题');
    expect(mapQuestionType('[多选题]')).toBe('多选题');
    expect(mapQuestionType('[判断题]')).toBe('判断题');
    expect(mapQuestionType('[填空题]')).toBe('填空题');
    expect(mapQuestionType('[简答题]')).toBe('简答题');
    expect(mapQuestionType('[计算题]')).toBe('计算题');
    expect(mapQuestionType('[自定义题型]')).toBe('自定义题型');
  });

  it('测试 API 接口 - 解析本地文件', async () => {
    // 模拟请求
    const result = await createHttpRequest(app).post('/word/parse').send({
      filePath: '不存在的文件.docx',
    });

    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.success).toBe(false);
    expect(result.body.message).toContain('文件不存在');
  });

  it('测试 API 接口 - 空文件路径', async () => {
    // 模拟请求
    const result = await createHttpRequest(app).post('/word/parse').send({
      filePath: '',
    });

    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.success).toBe(false);
    expect(result.body.message).toContain('文件路径不能为空');
  });

  it('测试 API 接口 - 缺少文件路径参数', async () => {
    // 模拟请求
    const result = await createHttpRequest(app).post('/word/parse').send({});

    // 验证响应
    expect(result.status).toBe(200);
    expect(result.body.success).toBe(false);
    expect(result.body.message).toContain('文件路径不能为空');
  });
});

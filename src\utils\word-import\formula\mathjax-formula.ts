/**
 * MathJax公式处理服务
 * 使用mathjax-node解析和渲染公式
 */

// 导入mathjax-node
const mjAPI = require('mathjax-node');

// 初始化MathJax
mjAPI.config({
  MathJax: {
    // 传统的MathJax配置
    SVG: {
      font: 'TeX',
      scale: 0.9, // 缩小公式大小
      minScaleAdjust: 80, // 最小缩放调整
      matchFontHeight: false, // 不匹配字体高度
      styles: {
        '.MathJax_SVG': {
          'font-size': '90%', // 设置字体大小为原来的90%
        },
      },
    },
  },
});

mjAPI.start();

/**
 * 将OMML公式转换为MathML
 * @param ommlElement OMML公式元素
 * @returns MathML字符串
 */
export function convertOMMLToMathML(ommlElement: Element): string {
  try {
    // 提取文本内容
    const text = ommlElement.textContent || '';

    // 清理文本内容
    const cleanedText = text.replace(/#/g, '').replace(/\s+/g, ' ').trim();

    // 将文本转换为MathML格式
    // 这里是一个简单的转换，实际应用中可能需要更复杂的处理
    return `<math xmlns="http://www.w3.org/1998/Math/MathML"><mtext>${cleanedText}</mtext></math>`;
  } catch (error) {
    console.error('OMML到MathML转换失败:', error);
    return '<math xmlns="http://www.w3.org/1998/Math/MathML"><mtext>Formula</mtext></math>';
  }
}

/**
 * 将MathML转换为LaTeX
 * @param mathml MathML字符串
 * @returns LaTeX字符串
 */
export async function convertMathMLToLaTeX(mathml: string): Promise<string> {
  try {
    // 使用MathJax将MathML转换为LaTeX
    const result = await mjAPI.typeset({
      math: mathml,
      format: 'MathML',
      mml: true,
      tex: true,
    });

    return result.tex || '';
  } catch (error) {
    console.error('MathML到LaTeX转换失败:', error);
    return '\\text{Formula}';
  }
}

/**
 * 将LaTeX转换为SVG
 * @param latex LaTeX字符串
 * @returns SVG字符串
 */
export async function convertLaTeXToSVG(latex: string): Promise<string> {
  try {
    // 使用MathJax将LaTeX转换为SVG
    const result = await mjAPI.typeset({
      math: latex,
      format: 'TeX',
      svg: true,
    });

    return result.svg || '';
  } catch (error) {
    console.error('LaTeX到SVG转换失败:', error);
    return createFallbackSVG();
  }
}

/**
 * 创建后备SVG（当转换失败时使用）
 * @returns SVG字符串
 */
function createFallbackSVG(): string {
  return `<svg xmlns="http://www.w3.org/2000/svg" width="60" height="20">
    <text x="5" y="15" font-family="sans-serif" font-size="10" fill="#666">公式</text>
  </svg>`;
}

/**
 * 将OMML公式直接转换为SVG
 * @param ommlElement OMML公式元素
 * @returns SVG字符串
 */
export async function convertOMMLToSVG(ommlElement: Element): Promise<string> {
  try {
    // 提取文本内容
    const text = ommlElement.textContent || '';

    // 清理文本内容
    const cleanedText = text.replace(/#/g, '').replace(/\s+/g, ' ').trim();

    // 尝试将文本转换为LaTeX
    // 这里是一个简单的转换，实际应用中可能需要更复杂的处理
    let latex = cleanedText;

    // 检查是否是二次方程公式
    if (
      cleanedText.includes('x') &&
      cleanedText.includes('=') &&
      (cleanedText.includes('-b') || cleanedText.includes('b')) &&
      cleanedText.includes('2a')
    ) {
      // 这很可能是二次方程公式
      latex = 'x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}';
    } else if (cleanedText.includes('=')) {
      // 一般方程式
      const parts = cleanedText.split('=');
      if (parts.length === 2) {
        const leftSide = parts[0].trim();
        const rightSide = parts[1].trim();

        // 处理右侧
        let processedRightSide = rightSide;

        // 处理平方
        processedRightSide = processedRightSide
          .replace(/b2/g, 'b^2')
          .replace(/a2/g, 'a^2')
          .replace(/c2/g, 'c^2')
          .replace(/x2/g, 'x^2')
          .replace(/y2/g, 'y^2');

        latex = `${leftSide} = ${processedRightSide}`;
      }
    }

    // 使用MathJax将LaTeX转换为SVG
    const result = await mjAPI.typeset({
      math: latex,
      format: 'TeX',
      svg: true,
      ex: 8, // 控制公式大小的单位，默认为6，减小这个值会使公式变小
      width: 80, // 控制公式宽度，默认为100
      linebreaks: true, // 允许公式自动换行
    });

    return result.svg || createFallbackSVG();
  } catch (error) {
    console.error('OMML到SVG转换失败:', error);
    return createFallbackSVG();
  }
}

// 不再需要这个函数，因为我们直接使用文本内容

/**
 * 将SVG转换为Data URL
 * @param svg SVG字符串
 * @returns Data URL
 */
export function convertSVGToDataURL(svg: string): string {
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
}

/**
 * 处理公式
 * @param ommlElement OMML公式元素
 * @returns 处理结果（SVG和Data URL）
 */
export async function processFormula(ommlElement: Element): Promise<{
  svg: string;
  dataURL: string;
}> {
  try {
    // 直接将OMML转换为SVG
    const svg = await convertOMMLToSVG(ommlElement);

    // 将SVG转换为Data URL
    const dataURL = convertSVGToDataURL(svg);

    return {
      svg,
      dataURL,
    };
  } catch (error) {
    console.error('处理公式失败:', error);

    // 如果转换失败，使用后备SVG
    const fallbackSVG = createFallbackSVG();
    const fallbackDataURL = convertSVGToDataURL(fallbackSVG);

    return {
      svg: fallbackSVG,
      dataURL: fallbackDataURL,
    };
  }
}

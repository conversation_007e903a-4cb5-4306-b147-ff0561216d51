import { Provide, Inject } from '@midwayjs/core';
import { NLPHelperService, NLPResult } from '../service/nlp-helper.service';
import {
  WordParserService,
  Question,
  QuestionType,
} from '../service/word-parser.service';

/**
 * NLP助手示例类
 * 展示如何在项目中使用NLPHelperService
 */
@Provide()
export class NLPHelperExample {
  @Inject()
  private nlpHelper: NLPHelperService;

  @Inject()
  private wordParser: WordParserService;

  /**
   * 处理单个Word文档并应用NLP分析
   * @param filePath Word文档路径
   * @returns 处理结果
   */
  async processWordFileWithNLP(filePath: string) {
    try {
      // 1. 初始化NLP服务
      await this.nlpHelper.init();

      // 2. 解析Word文档
      const parseResult = await this.wordParser.parseWordFile(filePath);
      if (!parseResult.success) {
        return {
          success: false,
          message: parseResult.message || '文档解析失败',
        };
      }

      // 3. 应用NLP处理
      const questions = parseResult.questions;
      const nlpResult = await this.nlpHelper.batchProcessQuestions(questions);

      // 4. 合并结果
      const enhancedQuestions = this.mergeNLPResults(questions, nlpResult);

      return {
        success: true,
        questions: enhancedQuestions,
        originalParseResult: parseResult,
        nlpAnalysis: nlpResult.data,
      };
    } catch (error) {
      console.error('处理文档失败:', error);
      return {
        success: false,
        message: `处理文档失败: ${error.message}`,
      };
    }
  }

  /**
   * 合并NLP分析结果到题目中
   * @param questions 原始题目列表
   * @param nlpResult NLP分析结果
   * @returns 增强后的题目列表
   */
  private mergeNLPResults(
    questions: Question[],
    nlpResult: NLPResult
  ): Question[] {
    if (!nlpResult.success || !nlpResult.data) {
      return questions;
    }

    const nlpData = nlpResult.data;
    return questions.map((question, index) => {
      // 如果有对应的NLP分析结果
      if (nlpData[index]) {
        const analysis = nlpData[index];

        // 更新题型（如果检测到了更准确的题型）
        if (analysis.detectedType && question.type === QuestionType.UNKNOWN) {
          question.type = analysis.detectedType;
        }

        // 添加知识点信息
        if (analysis.knowledgePoints && analysis.knowledgePoints.length > 0) {
          question['knowledgePoints'] = analysis.knowledgePoints;
        }
      }

      return question;
    });
  }

  /**
   * 检测相似题目示例
   * @param question 当前题目
   * @returns 相似题目列表
   */
  async findSimilarQuestions(question: Question) {
    try {
      // 初始化NLP服务
      await this.nlpHelper.init();

      // 设置相似度阈值为0.7（70%相似度）
      const similarityResult = await this.nlpHelper.detectSimilarQuestions(
        question,
        0.7
      );

      return {
        success: similarityResult.success,
        similarQuestions: similarityResult.data || [],
        message: similarityResult.message,
      };
    } catch (error) {
      console.error('查找相似题目失败:', error);
      return {
        success: false,
        similarQuestions: [],
        message: `查找相似题目失败: ${error.message}`,
      };
    }
  }

  /**
   * 单独进行知识点提取示例
   * @param text 文本内容
   * @returns 提取的知识点
   */
  async extractKnowledgePointsFromText(text: string) {
    try {
      // 初始化NLP服务
      await this.nlpHelper.init();

      // 提取知识点
      const result = await this.nlpHelper.extractKnowledgePoints(text);

      return {
        success: result.success,
        knowledgePoints: result.data || [],
        message: result.message,
      };
    } catch (error) {
      console.error('知识点提取失败:', error);
      return {
        success: false,
        knowledgePoints: [],
        message: `知识点提取失败: ${error.message}`,
      };
    }
  }
}
